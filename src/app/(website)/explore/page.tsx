"use client";

import { useEffect, useState } from "react";

import useS<PERSON> from "swr";
import fetcher from "@/lib/common/requests";

import { News as Card, Carousel, GridArt } from "@/components/common";
import { Section, Listing } from "@/modules";

const API_KEY = "f71f1099e905749db4c7c4238c014611";
const NORMAL_NEWS_BASE_URL = `https://gnews.io/api/v4?apikey=${API_KEY}&lang=en&country=us`;
const BREAKING_NEWS_BASE_URL = `https://gnews.io/api/v4/top-headlines?apikey=${API_KEY}&lang=en&country=us`;

const Explore = () => {
  // This due to budget constraints where we cannot accommodate this API cost
  const [ifNewsIsAvailable, setIfNewsIsAvailable] = useState(false);
  const url = `${BREAKING_NEWS_BASE_URL}&q=breaking%20news&category=business&max=1`;

  const { data } = useSWR(url, fetcher);

  useEffect(() => {
    const { articles } = data ?? {};
    setIfNewsIsAvailable(articles?.length > 0);
  }, [data]);

  return (
    <main className="w-full h-full">
      <GridArt amount={6} />
      {ifNewsIsAvailable && (
        <Section title="headline news today">
          <TopHeadLine />
        </Section>
      )}
      {ifNewsIsAvailable && (
        <Section title="latest news">
          <LatestNews />
        </Section>
      )}
      {ifNewsIsAvailable && (
        <Section title="articles">
          <Articles />
        </Section>
      )}
      <Section title="our feed">
        <OurFeed />
      </Section>
      {ifNewsIsAvailable && (
        <Section title="more news">
          <MoreNews />
        </Section>
      )}
    </main>
  );
};

const TopHeadLine = () => {
  const { data, isLoading, error } = useSWR(
    `${BREAKING_NEWS_BASE_URL}&q=breaking%20news&category=technology&max=1`,
    fetcher
  );

  if (data?.articles?.length === 0) return <></>;

  const NewsCard = (article: unknown) => {
    return <Card {...(article as object)} type="large" />;
  };

  const params = { data: data?.articles, isLoading, error };
  return <Listing request={params} grid={1} Component={NewsCard} />;
};

const LatestNews = () => {
  const { data, isLoading, error } = useSWR(
    `${BREAKING_NEWS_BASE_URL}&q=ai%20machine%20learning&category=business`,
    fetcher
  );

  if (data?.articles?.length === 0) return <></>;

  const NewsCard = (article: unknown) => {
    return <Card {...(article as object)} type="small" />;
  };

  const params = { data: data?.articles, isLoading, error };
  return (
    <div className="w-full h-[50em] overflow-y-scroll">
      <Listing request={params} grid={1} Component={NewsCard} />
    </div>
  );
};

const Articles = () => {
  const { data, isLoading, error } = useSWR(
    `${NORMAL_NEWS_BASE_URL}&q=ai%20artificial%20intelligence&category=space`,
    fetcher
  );

  if (data?.articles?.length === 0) return <></>;

  const NewsCard = (article: unknown) => {
    return <Card {...(article as object)} type="medium" />;
  };

  const params = { data: data?.articles, isLoading, error };
  return (
    <div className="w-full max-w-[100vw] overflow-x-scroll">
      <Listing
        request={params}
        grid={0}
        Component={NewsCard}
        className="flex flex-row gap-16"
      />
    </div>
  );
};

const OurFeed = () => {
  const { data, isLoading, error } = useSWR(
    "https://feeds.behold.so/sklzOCEvPCxz4MgdX0An",
    fetcher
  );

  const pictures = data?.posts?.map(
    (img: { sizes?: { large?: { mediaUrl?: string } } }) =>
      img?.sizes?.large?.mediaUrl
  );

  if (data?.posts?.length === 0) return <></>;

  return <Carousel pictures={pictures} />;
};

const MoreNews = () => {
  const { data, isLoading, error } = useSWR(
    `${NORMAL_NEWS_BASE_URL}&q=ai%20machine%20learning&category=technology`,
    fetcher
  );

  if (data?.articles?.length === 0) return <></>;

  const NewsCard = (article: unknown) => {
    return <Card {...(article as object)} type="medium" />;
  };

  const params = { data: data?.articles, isLoading, error };
  return <Listing request={params} grid={2} Component={NewsCard} />;
};

export default Explore;
