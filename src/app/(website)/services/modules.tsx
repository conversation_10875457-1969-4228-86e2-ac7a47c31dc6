"use client";

import { motion } from "framer-motion";
import { ListTab, RowTab, TestimonyCard } from "@/components/common";

interface ListLayoutProps {
  heading: {
    sub?: string;
    main: string;
  };
  services: unknown[];
  captions?: string[];
}

interface StaggeredLayoutProps {
  data?: unknown[];
}

interface ListTabsProps {
  width?: string;
  data?: unknown[];
  theme?: string;
}

interface RowTabsProps {
  list?: unknown[];
}

interface TestimonialsProps {
  data?: unknown[][];
}

export const ListLayout = ({
  heading,
  services,
  captions = [],
}: ListLayoutProps) => {
  return (
    <section className="w-full h-max flex flex-col gap-8 xl:gap-0 xl:flex-row justify-between items-start">
      <div className="flex flex-col">
        <div className="mb-8 md:mb-12 xl:mb-44 flex flex-col gap-2">
          {heading?.sub && (
            <h5 className="text-lg md:text-xl font-medium text-white/80">
              {heading.sub}
            </h5>
          )}
          <h3 className="text-2xl md:text-3xl lg:text-4xl text-lime-accent capitalize font-light">
            {heading?.main}
          </h3>
        </div>
        {captions?.map((caption: string, index: number) => {
          return (
            <p
              key={index}
              className="text-base md:text-lg text-navy-100 leading-relaxed"
            >
              {caption}
            </p>
          );
        })}
      </div>
      <ListTabs {...services} />
    </section>
  );
};

// Motion Settings
const animations = {
  initial: { scaleX: 0, opacity: 0 },
  whileInView: { scaleX: 1, opacity: 100 },
  viewport: { once: true },
  transition: {
    duration: 1,
    ease: "backInOut",
  },
};

export const StaggeredLayout = ({ data = [] }: StaggeredLayoutProps) => {
  return (
    <div className="w-full flex flex-col -gap-[1px]">
      {data?.map((value: unknown, index: number) => {
        return (
          <motion.div key={index} {...animations} className="origin-left">
            <ListTab {...(value as object)} />
          </motion.div>
        );
      })}
    </div>
  );
};

export const ListTabs = ({
  width = "w-full",
  data = [],
  theme = "lime",
}: ListTabsProps) => {
  return (
    <div className={`${width} flex flex-col gap-[1px]`}>
      {data?.map((value: unknown, index: number) => {
        return (
          <motion.div key={index} {...animations}>
            <ListTab
              {...(value as object)}
              style={{
                width: "w-full",
                theme: theme as "lime" | "outline" | "navy",
              }}
            />
          </motion.div>
        );
      })}
    </div>
  );
};

export const RowTabs = ({ list = [] }: RowTabsProps) => {
  return (
    <div className="w-full flex flex-col gap-[1px]">
      {list?.map((value: unknown, index: number) => {
        return (
          <motion.div key={index} {...animations} className="origin-top">
            <RowTab {...(value as object)} />
          </motion.div>
        );
      })}
    </div>
  );
};

export const Testimonials = ({ data = [] }: TestimonialsProps) => {
  return (
    <div className="w-full flex flex-col gap-4">
      {data?.map((column: unknown[], i: number) => {
        return (
          <div
            key={i}
            className="w-full flex flex-col gap-4 md:gap-0 md:flex-row md:gap-4"
          >
            {column?.map((row: unknown, j: number) => {
              return <TestimonyCard key={j} {...(row as any)} />;
            })}
          </div>
        );
      })}
    </div>
  );
};
