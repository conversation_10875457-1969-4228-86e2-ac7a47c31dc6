"use client";

import {
  corevalues,
  ourprocess,
  testimonials,
  coreServices,
  additionalServices,
} from "./data";

import { motion } from "framer-motion";

import { CallToActions } from "@/components/common";
import Pill from "@/components/common/ui/pill";
import { TypeAnimation } from "react-type-animation";

import { GiBullseye as AccuracyIcon } from "react-icons/gi";
import { SiOctopusdeploy as OctopusIcon } from "react-icons/si";
import { TbAutomaticGearbox as AutonomousIcon } from "react-icons/tb";

import DataWaveTheme from "@/modules/themes/datawave";
import { GridArt } from "@/components/common";
import { ListLayout, RowTabs, StaggeredLayout, Testimonials } from "./modules";

const Services = () => {
  return (
    <main className="w-full h-full flex flex-col gap-16 md:gap-20 lg:gap-24">
      <GridArt amount={5} />
      <DataWaveTheme>
        <Hero />
      </DataWaveTheme>
      <CoreServices />
      <Process />
      <CoreValues />
      {/* Until later */}
      {/* <Testimonial /> */}
    </main>
  );
};

const Hero = () => {
  const transition = { ease: "easeInOut", duration: 200 };

  return (
    <motion.section
      initial={false}
      animate={{ opacity: 100 }}
      transition={transition}
      exit={{ opacity: 0 }}
      className="h-screen -bottom-10 flex flex-col gap-12 xl:gap-0 xl:flex-row xl:justify-between xl:items-end"
    >
      <div className="w-full flex flex-col gap-8 items-start justify-center">
        <div className="flex flex-row gap-3">
          <Pill>
            <AutonomousIcon size={12} className="text-white" />
            <p className="text-sm font-medium">Autonomous</p>
          </Pill>
          <Pill>
            <AccuracyIcon size={14} className="text-white" />
            <p className="text-sm font-medium">Accurate</p>
          </Pill>
          <Pill>
            <OctopusIcon size={12} className="text-white" />
            <p className="text-sm font-medium">Multi-purpose</p>
          </Pill>
        </div>
        <CallToActions />
      </div>
      {/* Hero */}
      <div className="w-full max-w-3xl flex flex-col gap-8 items-end justify-center text-end">
        <motion.h1
          initial={false}
          animate={{ opacity: 100, transition }}
          transition={transition}
          className="text-5xl md:text-6xl lg:text-7xl font-medium bg-clip-text text-transparent bg-gradient-to-r from-zinc-600 to-zinc-500 mix-blend-darker leading-tight"
        >
          Today's Enterprise is
          <br />{" "}
          <TypeAnimation
            sequence={["Fast", 4000, "Agentic", 4000, "& Scalable", 4000]}
            wrapper="span"
            className="text-lime-accent"
            speed={6}
            repeat={Infinity}
          />{" "}
          Solutions
        </motion.h1>
      </div>
    </motion.section>
  );
};

const CoreServices = () => {
  return (
    <ListLayout
      heading={{
        sub: "We offer:",
        main: "Enterprise - AI First Architecture",
      }}
      services={coreServices}
      captions={
        [
          "Committed to excellence:",
          "ISO 27001:2022 A 8.25 and ISO/IEC/IEEE12207:2017 for SDLC",
        ] as string[]
      }
    />
  );
};

const AdditionalServices = () => {
  return (
    <ListLayout
      heading={{ main: "More - services" }}
      services={additionalServices}
    />
  );
};

const Process = () => {
  return (
    <section className="w-full flex flex-col gap-12 justify-start items-start">
      <div className="flex flex-col gap-2">
        <h3 className="text-2xl md:text-3xl lg:text-4xl text-lime-accent capitalize font-light">
          from vision to reality:
        </h3>
        <h3 className="text-2xl md:text-3xl lg:text-4xl text-white capitalize font-light">
          our process
        </h3>
      </div>
      <StaggeredLayout data={ourprocess} />
    </section>
  );
};

const CoreValues = () => {
  return (
    <section className="w-full flex flex-col justify-start items-start gap-16">
      <div className="flex flex-col gap-2">
        <h3 className="text-2xl md:text-3xl lg:text-4xl text-lime-accent capitalize font-light">
          our core values:{" "}
          <span className="text-white">
            building <br /> excellence together
          </span>
        </h3>
      </div>
      <RowTabs list={corevalues} />
    </section>
  );
};

const Testimonial = () => {
  return (
    <section className="w-full flex flex-col gap-16">
      <h3 className="text-2xl md:text-3xl lg:text-4xl text-white capitalize font-light">
        testimonials
      </h3>
      <Testimonials data={testimonials} />
    </section>
  );
};

export default Services;
