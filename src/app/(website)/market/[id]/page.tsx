"use client";

import { useEffect, useState } from "react";

import useS<PERSON> from "swr";

import fetcher from "@/lib/common/requests";
import { removeSpaces } from "@/lib/common/utils";
import { useUserInterface } from "@/providers/context";

import { motion } from "framer-motion";
import { usePathname, useSearchParams } from "next/navigation";

import { Thumbnailed as Card, GridArt, Carousel } from "@/components/common";
import { Section, Listing } from "@/modules";

// Icons
import { BsShare as ShareIcon } from "react-icons/bs";
import { IoCloseCircle as CloseIcon } from "react-icons/io5";

// Import required modules

const Modal = ({
  active,
  children,
}: {
  active: boolean;
  children: React.ReactNode;
}) => {
  const { setUserInterfaceState: _ } = useUserInterface();

  return (
    <div
      className={`w-[100vw] h-[100vh] bg-lime-500/[.5] ${
        active ? "fixed" : "hidden"
      } z-20`}
      onClick={() => {}}
    >
      <div className="z-40 fixed w-full h-full">{children}</div>
    </div>
  );
};

const Product = () => {
  const { userInterface } = useUserInterface();

  return (
    <main className="w-full h-full">
      <GridArt amount={6} />
      <Modal active={userInterface?.active}>
        <Checkout />
      </Modal>
      <section className="w-full flex flex-col gap-36 items-stretch justify-between text-white">
        <Layout />
      </section>
      <Section title="related products">
        <RelatedProductsData />
      </Section>
    </main>
  );
};

const Checkout = () => {
  const { setUserInterfaceState } = useUserInterface();

  const closeModal = () => {
    setUserInterfaceState({ id: "", component: "", active: false });
  };

  const Section = ({
    children,
    title,
    style,
  }: {
    children: React.ReactNode;
    title?: string;
    style?: string;
  }) => {
    return (
      <span
        className={`w-full flex flex-col gap-2 border-b border-lime-300 pb-4`}
      >
        {title && (
          <label className="font-bold text-lime-300 capitalize">{title}</label>
        )}
        <span className={style}>{children}</span>
      </span>
    );
  };

  const Form = () => {
    return (
      <form className="flex flex-col gap-4">
        <span className="gap-2">
          <label htmlFor="name">Name on card:</label>
          <input type="text" />
        </span>

        <span className="gap-2">
          <label htmlFor="card">Card number:</label>
          <input type="number" />
        </span>

        <span className="flex flex-row gap-4">
          <span className="w-full gap-2">
            <label htmlFor="expiry">Expiry:</label>
            <input type="date" />
          </span>

          <span className="w-full max-w-[30%] gap-2">
            <label htmlFor="cvv">CVV:</label>
            <input type="number" pattern="\d{1,3}" />
          </span>
        </span>
      </form>
    );
  };

  return (
    <div className="w-full flex flex-col gap-8 max-w-lg h-lg bg-lime-500 absolute p-6 z-40 top-[50%] -translate-y-[50%] right-0">
      <Section style="flex flex-row justify-between items-center">
        <h6>Checkout</h6>
        <CloseIcon
          size={25}
          className="cursor-pointer"
          onClick={() => closeModal()}
        />
      </Section>
      <Section title="order summary" style="flex flex-col gap-2">
        <p className="capitalize">product title:</p>
        <p>$49.98</p>
      </Section>
      <Section title="account" style="flex flex-row justify-between">
        <input type="email" value="" placeholder="<EMAIL>" readOnly />
      </Section>
      <Section title="payment details">
        <Form />
      </Section>
      <span className="w-full flex flex-row justify-between">
        <p className="capitalize">subtotal:</p>
        <p>$2939</p>
      </span>
      <span className="w-full flex flex-row gap-5">
        <button
          onClick={() => closeModal()}
          className="w-full border border-lime-50 hover:bg-lime-50 hover:text-black"
        >
          cancel
        </button>
        <button className="w-full bg-lime-350 hover:bg-lime-300">
          checkout
        </button>
      </span>
    </div>
  );
};

const Layout = () => {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const stateOn = searchParams.get("on");

  const query = pathname.split("/");

  const {
    data,
    isLoading: _,
    error: __,
  } = useSWR(`products/${stateOn ?? query[query.length - 1]}`, fetcher);

  const header = {
    title: data?.title,
    caption: data?.caption,
    price: data?.price,
    categories: data?.categories,
    date: data?.date?.split("T")[0],
  };

  const content = {
    details: data?.details,
    features: data?.features,
  };

  const gallery = {
    pictures: data?.gallery?.map(
      (img: any) => window.location.origin + img?.picture?.url
    ),
  };

  return (
    <span className="w-full h-full flex flex-col gap-20 pt-1 md:pt-6">
      <Header {...header} />
      <Carousel style="-mx-[3em]" {...gallery} />
      <Content {...content} />
    </span>
  );
};

const Header = ({
  title,
  caption,
  categories = [],
  price = 0,
  date,
}: {
  title: string;
  caption: string;
  categories?: any[];
  price?: number;
  date?: string;
}) => {
  const { setUserInterfaceState } = useUserInterface();

  const [copy, setCopy] = useState(false);
  const pathname = usePathname();

  useEffect(() => setCopy(false), [pathname]);

  const Categories = () => {
    return (
      <span className="w-max grid grid-cols-2 gap-1">
        {categories?.map((category: any, index: number) => {
          return (
            <p
              key={index}
              className="uppercase text-md font-semibold text-lime-250"
            >
              {category?.name}
            </p>
          );
        })}
      </span>
    );
  };

  const CallToAction = ({
    label,
    children,
  }: {
    label: string;
    children: React.ReactNode;
  }) => {
    return (
      <span className="w-full h-full flex flex-col gap-6 items-start">
        <label className="font-bold capitalize text-white">{label}</label>
        {children}
      </span>
    );
  };

  return (
    <motion.span
      initial={{ opacity: 0 }}
      animate={{ opacity: 100 }}
      transition={{ duration: 1, ease: "backIn" }}
      className="w-full flex flex-col items-start gap-8 justify-start xl:flex-row xl:items-start xl:justify-between z-10"
    >
      {/* title */}
      <motion.span
        initial={{ opacity: 0 }}
        animate={{ opacity: 100 }}
        transition={{ duration: 1.8, ease: "easeIn" }}
        className="w-full flex flex-col gap-2"
      >
        <h3 className="text-white">{title}</h3>
        {/* Caption */}
        <p className="text-navy-100 text-lg text-wrap max-w-5xl font-medium">
          {caption}
        </p>
      </motion.span>
      <span className="w-full max-w-lg flex flex-row gap-4">
        <CallToAction label={"Categories"}>
          <Categories />
          <button
            className="w-full solid neon font-semibold"
            onClick={() => {
              setUserInterfaceState({ id: "", component: "", active: true });
            }}
          >
            buy now for ${price}
          </button>
        </CallToAction>
        <CallToAction label={"Date Updated"}>
          <p>{date}</p>
          <button
            className="w-full outline lime"
            onClick={() => {
              navigator.clipboard.writeText(window.location.href);
              setCopy(true);
            }}
          >
            {copy ? (
              "Link copied ✅"
            ) : (
              <span className="flex flex-row items-center justify-center gap-2">
                <ShareIcon size={14} />
                <p>Share</p>
              </span>
            )}
          </button>
        </CallToAction>
      </span>
    </motion.span>
  );
};

const Content = ({
  features,
  details,
}: {
  features?: any[];
  details?: any[];
}) => {
  return (
    <section className="w-full flex flex-col xl:flex-row pt-12 gap-12 xl:gap-0 xl:gap-28 justify-start items-start">
      <span className="w-full max-w-5xl flex flex-col gap-8">
        {details?.map((detail: any, index: number) => {
          return (
            <span key={index} className="flex flex-col gap-4">
              <label className="text-lime-250 text-xl font-semibold">
                {detail?.title}
              </label>
              <p className="w-full text-navy-100 text-wrap font-light max-w-4xl">
                {detail?.description}
              </p>
            </span>
          );
        })}
      </span>
      <span className="flex flex-col gap-3">
        <label className="font-bold capitalize text-white">Features</label>
        {features?.map((feature: any, index: number) => {
          return (
            <p
              key={index}
              className="capitalize text-md font-semibold text-lime-250"
            >
              {feature?.name}
            </p>
          );
        })}
      </span>
    </section>
  );
};

const RelatedProductsData = () => {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const stateOn = searchParams.get("on");
  const query = pathname.split("/");

  const { data, isLoading, error } = useSWR(
    `products?where[id][not_equals]=${
      stateOn ?? query[query?.length - 1]
    }&depth=1&drafts=false&limit=2`,
    fetcher
  );

  if (data?.docs?.length === 0) return <></>;

  const ProductCard = (product: unknown) => {
    const { id, title, price } =
      (product as { id?: string; title?: string; price?: number }) ?? {};
    return (
      <Card
        {...(product as object)}
        caption={`Price: $${price}`}
        navigateTo={{
          internal: true,
          url: `/market/${removeSpaces(title || "")}/${id}`,
          params: { state: { on: id } },
        }}
      />
    );
  };

  const params = { data: data?.docs, isLoading, error };
  return <Listing request={params} grid={3} Component={ProductCard} />;
};

export default Product;
