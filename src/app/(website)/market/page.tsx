"use client";

import { Heading, Thumbnailed as Card, GridArt } from "@/components/common";
import { Section, Listing } from "@/modules";

// Request
import useSWR from "swr";
import fetcher from "@/lib/common/requests";

const Products = () => {
  return (
    <main className="w-full h-full">
      <GridArt amount={6} />
      <Section title="marketplace">
        <MarketplaceData />
      </Section>
    </main>
  );
};

const MarketplaceData = () => {
  const { data, isLoading, error } = useSWR(
    "products?depth=1&drafts=false",
    fetcher
  );

  if (data?.docs?.length === 0) return <></>;

  const ProductCard = (product: unknown) => {
    const { id, title, price } =
      (product as { id?: string; title?: string; price?: number }) ?? {};
    return (
      <Card
        {...(product as object)}
        caption={`Price: $${price}`}
        navigateTo={{
          internal: true,
          url: `market/${id}`,
          params: { state: { on: id } },
        }}
      />
    );
  };

  const params = { data: data?.docs, isLoading, error };
  return (
    <div className="w-full flex flex-col gap-8">
      <Heading
        tagline={"marketplace"}
        title={"Design, Code, Automate - All in One Marketplace"}
      />
      <div className="h-[80vh] overflow-y-auto">
        <Listing request={params} grid={3} Component={ProductCard} />
      </div>
    </div>
  );
};

export default Products;
