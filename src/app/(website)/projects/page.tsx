"use client";

import useS<PERSON> from "swr";
import fetcher from "@/lib/common/requests";
import { removeSpaces } from "@/lib/common/utils";
import { Thumbnailed as Card, GridArt } from "@/components/common";
import { Section, Listing } from "@/modules";

const Projects = () => {
  return (
    <main className="w-full h-full">
      <GridArt amount={6} />
      <Section title="projects">
        <ProjectsData />
      </Section>
    </main>
  );
};

const ProjectsData = () => {
  const { data, isLoading, error } = useSWR(
    "projects?depth=1&drafts=false",
    fetcher
  );

  if (data?.docs?.length === 0) return <></>;

  const ProjectCard = (project: unknown) => {
    const { title, id } = (project as { title?: string; id?: string }) ?? {};
    return (
      <Card
        {...(project as object)}
        navigateTo={{
          internal: true,
          url: `/projects/${id}`,
          params: { state: { on: id } },
        }}
      />
    );
  };

  const params = { data: data?.docs, isLoading, error };
  return <Listing request={params} grid={2} Component={ProjectCard} />;
};

export default Projects;
