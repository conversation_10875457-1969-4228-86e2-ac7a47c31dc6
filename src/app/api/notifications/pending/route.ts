import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { notificationService } from "@/lib/api/services/notification";

/**
 * GET /api/notifications/pending
 * Get pending notifications for background sync
 */
export async function GET(request: NextRequest) {
  try {
    // Get user session
    const session: any = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Initialize notification service with context
    await notificationService.initialize({
      session,
      user: session.user,
      request,
    });

    // Get pending notifications (unread notifications from last 24 hours)
    const twentyFourHoursAgo = new Date();
    twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24);

    const queryParams = {
      userId: session.user.id,
      isRead: false,
      createdAfter: twentyFourHoursAgo.toISOString(),
      limit: 50,
      sortBy: "createdAt" as const,
      sortOrder: "desc" as const,
    };

    // Use the service to get pending notifications
    const result = await notificationService.getNotifications(queryParams);

    if (!result.success) {
      return NextResponse.json(
        { 
          error: result.message || "Failed to fetch pending notifications",
          notifications: []
        },
        { status: result.statusCode || 500 }
      );
    }

    // Transform notifications for service worker consumption
    const pendingNotifications = (result.data?.notifications || []).map((notification: any) => ({
      id: notification.id,
      title: notification.title,
      body: notification.message,
      icon: "/favicons/android-chrome-192x192.png",
      badge: "/favicons/favicon-32x32.png",
      tag: `notification-${notification.id}`,
      data: {
        id: notification.id,
        type: notification.type,
        userId: notification.userId,
        url: getNotificationUrl(notification),
        trackDismissal: true,
        ...notification.data
      },
      timestamp: new Date(notification.createdAt).getTime(),
      requireInteraction: notification.priority === "high" || notification.priority === "urgent"
    }));

    return NextResponse.json({
      success: true,
      notifications: pendingNotifications,
      count: pendingNotifications.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error("Error fetching pending notifications:", error);
    return NextResponse.json(
      { 
        error: "Internal server error",
        notifications: []
      },
      { status: 500 }
    );
  }
}

/**
 * Generate appropriate URL for notification based on type
 */
function getNotificationUrl(notification: any): string {
  const baseUrl = "/dashboard";
  
  switch (notification.type) {
    case "chat":
      return notification.data?.roomId 
        ? `${baseUrl}/chat/${notification.data.roomId}`
        : `${baseUrl}/chat`;
    
    case "contract":
      return notification.data?.contractId
        ? `${baseUrl}/contracts/${notification.data.contractId}`
        : `${baseUrl}/contracts`;
    
    case "proposal":
      return notification.data?.proposalId
        ? `${baseUrl}/proposals/${notification.data.proposalId}`
        : `${baseUrl}/proposals`;
    
    case "room":
      return notification.data?.roomId
        ? `${baseUrl}/rooms/${notification.data.roomId}`
        : `${baseUrl}/rooms`;
    
    case "systemAlerts":
      return `${baseUrl}/settings/notifications`;
    
    case "roleChanges":
      return `${baseUrl}/settings/account`;
    
    default:
      return baseUrl;
  }
}
