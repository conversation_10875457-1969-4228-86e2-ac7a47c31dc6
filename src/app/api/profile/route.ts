import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { ProfileService } from "@/lib/api/services/profile";

/**
 * GET /api/rbac/profile
 * Get user profile (defaults to current user if no userId provided)
 */
export async function GET(request: NextRequest) {
  try {
    const session: any = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get("userId") || session.user.id;

    // Create profile service instance with context
    const profileService = new ProfileService({
      requireAuth: true,
      enableLogging: true,
      validateInput: true,
      validateOutput: false,
    });

    profileService.setContext({
      session,
      user: session.user,
      request,
    });

    // Use service to get profile
    const result = await profileService.getProfileByUserId(userId);

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data,
        message: result.data
          ? "Profile retrieved successfully"
          : "No profile found",
        timestamp: result.timestamp,
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          error: true,
          message: result.message || "Failed to retrieve profile",
          errors: result.errors,
          timestamp: result.timestamp,
        },
        { status: result.statusCode || 500 }
      );
    }
  } catch (error) {
    console.error("Profile retrieval error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message:
          error instanceof Error ? error.message : "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/rbac/profile
 * Upsert user profile (create or update)
 */
export async function POST(request: NextRequest) {
  try {
    const session: any = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();

    // Create profile service instance with context
    const profileService = new ProfileService({
      requireAuth: true,
      enableLogging: true,
      validateInput: true,
      validateOutput: false,
    });

    profileService.setContext({
      session,
      user: session.user,
      request,
    });

    // Use service to upsert profile
    const result = await profileService.upsertProfile(body);

    if (result.success) {
      return NextResponse.json(
        {
          success: true,
          data: result.data,
          message: "Profile updated successfully",
          timestamp: result.timestamp,
        },
        { status: 200 }
      );
    } else {
      return NextResponse.json(
        {
          success: false,
          error: true,
          message: result.message || "Failed to update profile",
          errors: result.errors,
          timestamp: result.timestamp,
        },
        { status: result.statusCode || 400 }
      );
    }
  } catch (error) {
    console.error("Profile upsert error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message:
          error instanceof Error ? error.message : "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/rbac/profile
 * Delete user profile
 */
export async function DELETE(request: NextRequest) {
  try {
    const session: any = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Parse request body to get userId (optional, defaults to current user)
    const body = await request.json();
    const userId = body.userId || session.user.id;

    // Create profile service instance with context
    const profileService = new ProfileService({
      requireAuth: true,
      enableLogging: true,
      validateInput: true,
      validateOutput: false,
    });

    profileService.setContext({
      session,
      user: session.user,
      request,
    });

    // Use service to delete profile
    const result = await profileService.deleteProfile(userId);

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data,
        message: result.message || "Profile deleted successfully",
        timestamp: result.timestamp,
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          error: true,
          message: result.message || "Failed to delete profile",
          errors: result.errors,
          timestamp: result.timestamp,
        },
        { status: result.statusCode || 500 }
      );
    }
  } catch (error) {
    console.error("Profile deletion error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message:
          error instanceof Error ? error.message : "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
