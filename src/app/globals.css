@import "tailwindcss";
@import "tw-animate-css";

/* Font Face Declaration - Migrated from font.css */
@font-face {
    font-family: "Manrope";
    src: url('/Manrope/Manrope-VariableFont_wght.ttf') format('truetype');
    font-display: swap;
}

@custom-variant dark (&:is(.dark *));

:root {
    --neon: #00FF99;
    --navy: #051928;
    --white: #ffffffde;
    --black: #000000;
    --radius: 0.425rem;
}

:root {
  font-family: 'Manrope', sans-serif;
  color-scheme: light dark;
  color: var(--white);
  background-color: #000000;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

@theme {
    /* Font Family - Use Manrope as the default sans font */
    --font-family-sans: 'Manrope', ui-sans-serif, system-ui, sans-serif;

    /* Lime color palette */
    --color-lime-50: #F7FCFA;
    --color-lime-100: #EBFAF4;
    --color-lime-150: #BEF4DE;
    --color-lime-200: #25F4A1;
    --color-lime-250: #00E58A;
    --color-lime-300: #09AA6A;
    --color-lime-350: #0D734A;
    --color-lime-400: #105B3D;
    --color-lime-450: #071D14;
    --color-lime-500: #060E0B;
    /* Navy color palette */
    --color-navy-50: #E7EBEE;
    --color-navy-100: #BACCD8;
    --color-navy-150: #649EC9;
    --color-navy-200: #18517C;
    --color-navy-250: #051928;
    --color-navy-300: #050E14;
    --color-navy-350: #010609;
}

html, body {
  top: 0;
  left: 0;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  scroll-behavior: smooth;
  transition: .3s all ease-in-out;
  /* background-color: var(--black); */
}

a:hover {
  color: var(--neon)
}

/* Brand Color Utilities - Keep only essential brand colors */
@layer utilities {
  .text-neon { color: var(--neon); }
  .text-navy { color: var(--navy); }
  .text-lime-accent { color: var(--color-lime-200); }
  .text-lime-bright { color: var(--color-lime-250); }
}

.top-bar {
  width: 100%;
  z-index: 33;
  padding: 52px;
  position: absolute;
  background: transparent;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}

.logo {
  width: auto;
  max-height: 45px;
}

/* Company */
.company-logo-wrap {
  margin-right: 40px;
}

.partnership-logo-wrap {
  border-right: solid 1px #051928;
}

.partnership-logo {
  cursor: pointer;
  max-height: 80px;
}

.company-logo {
  cursor: pointer;
  max-height: 30px;
}

.company-logo-wrap, .company-logo {
  width: auto;
  min-width: 300px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.saturation-reveal {
  filter: saturate(0);
  transition: .3s all ease-in-out;
}

.saturation-reveal:hover {
  filter: saturate(100%);
}

.scroll-arrow-container {
  width: screen;
  height: 40px;
  position: relative;
  z-index: 2;
  left: 50%;
  transform: translateX(-50%);
  top: 80vh;
}

.scroll-arrow-icon {
  top: -20px;
  position: absolute;
  align-content: center;
  justify-self: center;
  animation: 1.5s infinite linear scrolldown;
}

/* Payment Information Input Fields */
#card-number, #expiration-date, #cvv, #card-number input, #expiration-date input, #cvv input {
  height: 20px;
  padding: 8px;
}

@keyframes scrolldown {
  0% {
    opacity: 0; /* Arrow disappears */
    top: -30px; /* Start position */
  }
  50% {
      opacity: 1; /* Arrow appears */
      top: calc(50% - 6.5px); /* Middle position */
  }
  100% {
      opacity: 0; /* Arrow disappears */
      top: calc(100% + 30px); /* End position */
  }
}

.card, .banner-hero {
  width: 100%;
  display: flex;
  overflow: hidden;
  position: relative;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.card {
  height: 30rem;
  cursor: pointer;
}

.banner-hero {
  height: 100vh;
}

.content {
  width: 100%;
  height: 100%;
  display: flex;
  padding: 2.5rem;
  align-items: flex-start;
  justify-content: flex-end;
  flex-direction: column;
  gap: 10px;
  z-index: 7;
}

.overlay {
  z-index: 6;
  width: 100%;
  height: 100%;
  display: flex;
  position: absolute;
  transform-origin: bottom;
  transition: .2s all ease-in;
}

.overlay-nrm {
  background: linear-gradient(0deg, rgb(5, 25, 40), rgba(5, 25, 40, .5), rgba(5, 25, 40, .1),  rgba(5, 25, 40, 0));
}

.overlay-hvr {
  background: rgba(5, 25, 40, .7)
}

.marquee {
  position: relative;
  width: 100vw;
  max-width: 100%;
  height: 50px;
  overflow-x: hidden;
}

.track {
  display: flex;
  place-content: center center;
  position: absolute;
  animation: marquee 28s linear infinite;
}

@keyframes marquee {
  from { transform: translateX(0); }
  to { transform: translateX(-50%); }
}

.progress-bar {
  z-index: 4;
  top:0;
  left: 0;
  right: 0;
  height: 5px;
  position: fixed;
  transform-origin: 0%;
  background: var(--neon);
}

/* Gallery Pagination */
.pagination {
  width: max-content;
  height: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.pagination > .page {
  cursor: pointer;  
  transition: .4s all ease-in;
}

.pagination > .page:hover {
  transform: scale(1.25);
}

.pagination > .active {
  color: var(--black);
}

.pagination > .inactive {
  color: #94a3b8;
}

/* Removing Arrows */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
   -webkit-appearance: none;
}

footer {
  padding: 7rem 3rem;
  display: flex;
  flex-direction: column;
  gap: 6rem;
  width: 100%;
  height: 100%;
  position: relative;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

footer > .overlay {
  z-index: 2;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100%;
  position: absolute;  
}

footer > .globe {
  z-index: 10;
  opacity: 100%;
  top: 33%;
  left: 50%;
  width: 30em;
  height: 30em;
  padding: 4rem;
  position: absolute;
  transform: translate(-50%, -50%);
  background-image: url('/img/earth.png');
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

:root {
    --background: oklch(1 0 0);
    --foreground: oklch(0.145 0 0);
    --card: oklch(1 0 0);
    --card-foreground: oklch(0.145 0 0);
    --popover: oklch(1 0 0);
    --popover-foreground: oklch(0.145 0 0);
    --primary: oklch(0.145 0 0);
    --primary-foreground: oklch(0.985 0 0);
    --secondary: oklch(0.961 0 0);
    --secondary-foreground: oklch(0.145 0 0);
    --muted: oklch(0.961 0 0);
    --muted-foreground: oklch(0.455 0 0);
    --accent: oklch(0.961 0 0);
    --accent-foreground: oklch(0.145 0 0);
    --destructive: oklch(0.627 0.265 29.233);
    --border: oklch(0.145 0 0 / 10%);
    --input: oklch(0.145 0 0 / 10%);
    --ring: oklch(0.556 0 0);
    --radius: 0.5rem;
    --chart-1: oklch(0.488 0.243 264.376);
    --chart-2: oklch(0.696 0.17 162.48);
    --chart-3: oklch(0.769 0.188 70.08);
    --chart-4: oklch(0.627 0.265 303.9);
    --chart-5: oklch(0.645 0.246 16.439);
    --sidebar: oklch(1 0 0);
    --sidebar-foreground: oklch(0.145 0 0);
    --sidebar-primary: oklch(0.488 0.243 264.376);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.961 0 0);
    --sidebar-accent-foreground: oklch(0.145 0 0);
    --sidebar-border: oklch(0.145 0 0 / 10%);
    --sidebar-ring: oklch(0.556 0 0);
}


@theme inline {
    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);
    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);
    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);
    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);
    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);
    --color-destructive: var(--destructive);
    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);
    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);
    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
}

.dark {
    --background: oklch(0.145 0 0);
    --foreground: oklch(0.985 0 0);
    --card: oklch(0.205 0 0);
    --card-foreground: oklch(0.985 0 0);
    --popover: oklch(0.205 0 0);
    --popover-foreground: oklch(0.985 0 0);
    --primary: oklch(0.922 0 0);
    --primary-foreground: oklch(0.205 0 0);
    --secondary: oklch(0.269 0 0);
    --secondary-foreground: oklch(0.985 0 0);
    --muted: oklch(0.269 0 0);
    --muted-foreground: oklch(0.708 0 0);
    --accent: oklch(0.269 0 0);
    --accent-foreground: oklch(0.985 0 0);
    --destructive: oklch(0.704 0.191 22.216);
    --border: oklch(1 0 0 / 10%);
    --input: oklch(1 0 0 / 15%);
    --ring: oklch(0.556 0 0);
    --chart-1: oklch(0.488 0.243 264.376);
    --chart-2: oklch(0.696 0.17 162.48);
    --chart-3: oklch(0.769 0.188 70.08);
    --chart-4: oklch(0.627 0.265 303.9);
    --chart-5: oklch(0.645 0.246 16.439);
    --sidebar: oklch(0.205 0 0);
    --sidebar-foreground: oklch(0.985 0 0);
    --sidebar-primary: oklch(0.488 0.243 264.376);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.269 0 0);
    --sidebar-accent-foreground: oklch(0.985 0 0);
    --sidebar-border: oklch(1 0 0 / 10%);
    --sidebar-ring: oklch(0.556 0 0);
}
