"use client";

import { use<PERSON><PERSON><PERSON>, redirect } from "next/navigation";

import { <PERSON><PERSON> } from "@/components/common/ui/button";
import { Badge } from "@/components/common/ui/badge";
import { Card, CardContent } from "@/components/common/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/common/ui/dropdown-menu";
import { LottieIconPlayer, LottieIconLib } from "@/components/common/lottie";
import {
  Calendar,
  Clock,
  DollarSign,
  FileText,
  CheckCircle,
  AlertCircle,
  Send,
  Eye,
  Shield,
  Zap,
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import type { Proposal } from "@/lib/api/validators/schemas/proposal";

interface ProposalDetailsHeaderProps {
  proposal: Proposal;
  onEdit: () => void;
  onDelete: () => void;
  onSendReminder: () => void;
  isUpdating: boolean;
  isDeleting: boolean;
}

const statusConfig = {
  created: {
    label: "Created",
    variant: "secondary" as const,
    icon: AlertCircle,
    color: "text-gray-500",
  },
  submitted: {
    label: "Submitted",
    variant: "default" as const,
    icon: Send,
    color: "text-blue-500",
  },
  received: {
    label: "Received",
    variant: "default" as const,
    icon: Eye,
    color: "text-purple-500",
  },
  negotiating: {
    label: "Negotiating",
    variant: "default" as const,
    icon: AlertCircle,
    color: "text-orange-500",
  },
  agreed: {
    label: "Agreed",
    variant: "default" as const,
    icon: Shield,
    color: "text-green-500",
  },
  inprogress: {
    label: "In Progress",
    variant: "default" as const,
    icon: Zap,
    color: "text-blue-500",
  },
  reviewing: {
    label: "Reviewing",
    variant: "default" as const,
    icon: Eye,
    color: "text-yellow-500",
  },
  completed: {
    label: "Completed",
    variant: "default" as const,
    icon: CheckCircle,
    color: "text-green-500",
  },
};

export function ProposalDetailsHeader({
  proposal,
  onEdit,
  onDelete,
  onSendReminder,
  isUpdating,
  isDeleting,
}: ProposalDetailsHeaderProps) {
  const { slug } = useParams();
  const status = statusConfig[proposal.status] || statusConfig.created;
  const StatusIcon = status.icon;

  // Check if reminder can be sent (after a week and limited to 3 times)
  const canSendReminder = () => {
    const createdDate = new Date(proposal.createdAt);
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    // For now, we'll assume reminders can be sent if proposal is older than a week
    // In a real implementation, you'd track reminder count in the database
    return (
      createdDate < oneWeekAgo &&
      proposal.status !== "completed" &&
      proposal.status !== "agreed"
    );
  };

  const handleBack = () => {
    redirect(`/${slug}/proposals`);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  return (
    <div className="grid grid-cols-1 gap-16">
      {/* Main Header */}
      <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-12">
        <div className="flex flex-col gap-6">
          {" "}
          <div className="flex items-end gap-4 pt-1">
            <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900">
              <FileText className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <p className="text-gray-900 dark:text-zinc-500 font-semibold">
              Proposal
            </p>
          </div>
          <div className="flex flex-col items-start gap-3">
            <h1 className="text-3xl font-bold text-foreground">
              {proposal.name}
            </h1>
            <Badge variant={status.variant} className="flex items-center gap-1">
              <StatusIcon className={`h-3 w-3 ${status.color}`} />
              {status.label}
            </Badge>
          </div>
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              Created{" "}
              {formatDistanceToNow(new Date(proposal.createdAt), {
                addSuffix: true,
              })}
            </div>
            <div className="flex items-center gap-1">
              <Clock className="h-4 w-4" />
              Updated{" "}
              {formatDistanceToNow(new Date(proposal.updatedAt), {
                addSuffix: true,
              })}
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-4 mt-6">
          <Button
            variant="outline"
            size="sm"
            onClick={handleBack}
            disabled={isUpdating || isDeleting}
            className="flex items-center gap-2"
          >
            <LottieIconPlayer icon={LottieIconLib.arrowRight} size={16} />
            Back to Proposals
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                disabled={isUpdating || isDeleting}
                className="flex items-center gap-2"
              >
                <LottieIconPlayer icon={LottieIconLib.menu} size={16} />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={onEdit}
                disabled={isUpdating || isDeleting}
                className="flex items-center gap-2"
              >
                <LottieIconPlayer icon={LottieIconLib.edit} size={16} />
                {isUpdating ? "Updating..." : "Edit"}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={onDelete}
                disabled={isUpdating || isDeleting}
                variant="destructive"
                className="flex items-center gap-2"
              >
                <LottieIconPlayer icon={LottieIconLib.delete} size={16} />
                {isDeleting ? "Deleting..." : "Delete"}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
        <Card>
          <CardContent className="px-4">
            <div className="flex flex-col items-start gap-3">
              <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
                <DollarSign className="h-5 w-5 text-green-600 dark:text-green-400" />
              </div>
              <div className="flex flex-col gap-1">
                <p className="text-sm text-muted-foreground">Total Budget</p>
                <p className="text-xl font-semibold">
                  {formatCurrency(proposal.total_budget)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="px-4">
            <div className="flex flex-col items-start gap-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                <Clock className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="flex flex-col gap-1">
                <p className="text-sm text-muted-foreground">Duration</p>
                <p className="text-xl font-semibold">
                  {proposal.duration} days
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="px-4">
            <div className="flex flex-col items-start gap-3">
              <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                <CheckCircle className="h-5 w-5 text-purple-600 dark:text-purple-400" />
              </div>
              <div className="flex flex-col gap-1">
                <p className="text-sm text-muted-foreground">Terms Agreed</p>
                <p className="text-xl font-semibold">
                  {proposal.agreed_to_terms_and_conditions ? "Yes" : "No"}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
