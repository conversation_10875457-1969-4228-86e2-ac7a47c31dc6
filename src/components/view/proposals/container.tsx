"use client";

import { useState, useEffect } from "react";
import { redirect, useParams } from "next/navigation";

import { useProposal } from "@/hooks/useProposal";
import { ProposalHeader } from "./header";
import { ProposalStatistics } from "./statistics";
import {
  CreateProposalDialog,
  type CreateProposalFormData,
} from "./create-dialog";
import { ProposalTable } from "./table";
import { type Proposal } from "@/components/common/types";
import type { Proposal as ApiProposal } from "@/lib/api/validators/schemas/proposal";

// Type adapter to convert API proposals to UI proposals
const adaptApiProposalToUI = (apiProposal: ApiProposal): Proposal => {
  // Map API status to UI status
  const statusMap: Record<string, Proposal["status"]> = {
    created: "draft",
    submitted: "pending",
    received: "pending",
    negotiating: "pending",
    agreed: "approved",
    inprogress: "approved",
    reviewing: "pending",
    completed: "completed",
  };

  return {
    id: apiProposal.id,
    name: apiProposal.name,
    client: apiProposal.client?.name || "",
    description: apiProposal.description || "",
    status: statusMap[apiProposal.status] || "draft",
    budgetType: apiProposal.fixed_budget > 0 ? "fixed" : "milestone",
    fixedBudget: apiProposal.fixed_budget,
    totalBudget: apiProposal.total_budget,
    duration: apiProposal.duration,
    attachments: apiProposal.links || [],
    createdDate: new Date(apiProposal.createdAt).toISOString(),
    lastModified: new Date(apiProposal.updatedAt).toISOString(),
    milestones: apiProposal.milestones || [],
    agreed_to_terms_and_conditions: apiProposal.agreed_to_terms_and_conditions,
  };
};

export function ProposalContainer() {
  const { slug } = useParams();

  const {
    proposals,
    statistics,
    isLoading,
    isCreating,
    isLoadingStats,
    error,
    create,
    clearError,
  } = useProposal();

  const [searchTerm, setSearchTerm] = useState("");
  const [isFormOpen, setIsFormOpen] = useState(false);

  // Adapt API proposals to UI format
  const adaptedProposals: Proposal[] = proposals.map(adaptApiProposalToUI);

  // Clear errors when component unmounts
  useEffect(() => {
    return () => {
      if (error) {
        clearError();
      }
    };
  }, [error]);

  const handleCreateProposal = async (formData: CreateProposalFormData) => {
    try {
      // Transform form data to match API schema
      const proposalData = {
        name: formData.name,
        description: formData.description,
        status: "created" as const,
        links: [], // Will be populated from attachments if needed
        milestones:
          formData.budgetType === "milestone" ? formData.milestones : [],
        fixed_budget:
          formData.budgetType === "fixed" ? formData.fixedBudget : 0,
        total_budget:
          formData.budgetType === "fixed"
            ? formData.fixedBudget
            : formData.milestones.reduce(
                (total, milestone) => total + milestone.amount,
                0
              ),
        duration: formData.duration,
        agreed_to_terms_and_conditions: formData.agreed_to_terms_and_conditions,
      };

      await create(proposalData);
      setIsFormOpen(false);
    } catch (error) {
      console.error("Failed to create proposal:", error);
    }
  };

  const handleViewProposal = (proposal: Proposal) => {
    // Handle view proposal logic
    redirect(`/${slug}/proposals/${proposal.id}`);
  };

  return (
    <div className="space-y-8 p-4">
      {/* Header */}
      <ProposalHeader
        title="Proposals"
        subtitle="Manage and track all your project proposals in one place"
        onCreateProposal={() => setIsFormOpen(true)}
      />

      {/* Error Display */}
      {error && (
        <div className="bg-destructive/10 border border-destructive/20 text-destructive px-4 py-3 rounded-md">
          <p className="text-sm">{error}</p>
          <button
            onClick={clearError}
            className="text-destructive hover:text-destructive/80 text-sm underline ml-2"
          >
            Dismiss
          </button>
        </div>
      )}

      {/* Statistics Cards */}
      <ProposalStatistics statistics={statistics} isLoading={isLoadingStats} />

      {/* Create Proposal Form */}
      <div className="flex justify-end">
        <CreateProposalDialog
          editMode={false}
          isOpen={isFormOpen}
          onOpenChange={setIsFormOpen}
          onSubmit={handleCreateProposal}
          isCreating={isCreating}
          isEditing={false}
        />
      </div>

      {/* Proposals Table */}
      <ProposalTable
        proposals={adaptedProposals}
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        isLoading={isLoading}
        onView={handleViewProposal}
        pagination={{
          currentPage: 1,
          totalPages: 1,
          totalItems: adaptedProposals.length,
          itemsPerPage: adaptedProposals.length,
          onPageChange: () => {},
        }}
      />
    </div>
  );
}
