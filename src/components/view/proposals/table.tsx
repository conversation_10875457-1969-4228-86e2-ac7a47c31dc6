import { But<PERSON> } from "@/components/common/ui/button";
import { Badge } from "@/components/common/ui/badge";
import { FileText } from "lucide-react";
import { IoFolderOpenOutline as OpenFileIcon } from "react-icons/io5";
import { Listing } from "@/layouts/dashboard/details/basic";
import type { Proposal } from "@/components/common/types";

interface ProposalTableProps {
  proposals: Proposal[];
  isLoading: boolean;
  onView: (proposal: Proposal) => void;
}

export function ProposalTable({
  proposals,
  isLoading,
  onView,
}: ProposalTableProps) {
  const columns = [
    {
      key: "name",
      label: "Proposal Name",
      render: (proposal: Proposal) => (
        <div>
          <div className="font-medium text-foreground">{proposal.name}</div>
          <div className="text-sm text-muted-foreground truncate max-w-xs">
            {proposal.description}
          </div>
        </div>
      ),
    },
    {
      key: "status",
      label: "Status",
      render: (proposal: Proposal) => {
        const statusColors = {
          approved:
            "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-300",
          pending:
            "bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-300",
          rejected: "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-300",
          draft:
            "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300",
          completed:
            "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-300",
        };

        return (
          <Badge
            variant="secondary"
            className={
              statusColors[proposal.status as keyof typeof statusColors] ||
              statusColors.draft
            }
          >
            {proposal.status.charAt(0).toUpperCase() + proposal.status.slice(1)}
          </Badge>
        );
      },
    },
    {
      key: "budgetType",
      label: "Budget Type",
      render: (proposal: Proposal) => (
        <span className="text-sm text-foreground capitalize">
          {proposal.budgetType}
        </span>
      ),
    },
    {
      key: "totalBudget",
      label: "Total Budget",
      render: (proposal: Proposal) => (
        <span className="text-sm font-medium text-foreground">
          ${proposal.totalBudget.toLocaleString()}
        </span>
      ),
    },
    {
      key: "duration",
      label: "Duration",
      render: (proposal: Proposal) => (
        <span className="text-sm text-foreground">
          {proposal.duration} weeks
        </span>
      ),
    },
    {
      key: "createdDate",
      label: "Created Date",
      render: (proposal: Proposal) => (
        <span className="text-sm text-foreground">
          {new Date(proposal.createdDate).toLocaleDateString()}
        </span>
      ),
    },
    {
      key: "actions",
      label: "Actions",
      render: (proposal: Proposal) => (
        <Button variant="ghost" size="sm" onClick={() => onView(proposal)}>
          <OpenFileIcon />
          Open
        </Button>
      ),
    },
  ];

  return (
    <Listing.Table
      id="proposals-table"
      data={proposals}
      columns={columns}
      loading={isLoading}
      emptyState={
        <div className="text-center py-12">
          <FileText className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
            No proposals
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Get started by creating your first proposal
          </p>
        </div>
      }
    />
  );
}
