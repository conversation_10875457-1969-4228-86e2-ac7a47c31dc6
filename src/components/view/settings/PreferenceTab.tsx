"use client";

import { cn } from "@/lib/utils";
import { usePreference } from "@/hooks/usePreference";

import { Card, CardContent } from "@/components/common/ui/card";

import { MoonStar, SunMedium } from "lucide-react";
import { LottieIconPlayer, LottieIconLib } from "@/components/common/lottie";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/common/ui/select";

export const PreferenceTab = () => {
  return (
    <div className="space-y-8">
      <Section
        about={{
          title: "Language and Time",
          description: "Set your default locale, date format and timezone",
        }}
      >
        <MiscellaneousSettings />
      </Section>
      <Section
        about={{
          title: "User Interface Mode",
          description: "Set your default view mode",
        }}
      >
        <SettingTab
          id="layout"
          value="basic"
          title="Basic Layout"
          description="Default paginated view"
          icon={<LottieIconPlayer icon={LottieIconLib.basicLayout} size={40} />}
        />
        <SettingTab
          id="layout"
          value="advanced"
          title="Advanced Layout"
          description="Scrolling view"
          icon={
            <LottieIconPlayer icon={LottieIconLib.advancedLayout} size={40} />
          }
        />
      </Section>
      <Section
        about={{
          title: "Theme",
          description: "Set your default theme",
        }}
        className="flex flex-row gap-6"
      >
        <SettingTab
          id="theme"
          value="light"
          title="Light"
          description=""
          icon={<SunMedium size={29} />}
        />
        <SettingTab
          id="theme"
          value="dark"
          title="Dark"
          description=""
          icon={<MoonStar size={29} />}
        />
      </Section>
    </div>
  );
};

const Section = ({
  about,
  className,
  children,
}: {
  about: { title: string; description: string };
  className?: string;
  children: React.ReactNode;
}) => {
  return (
    <div className="space-y-6">
      <div className="space-y-1">
        <h2 className="text-lg font-semibold">{about.title}</h2>
        <p className="text-sm text-zinc-600">{about.description}</p>
      </div>
      <div className={cn("space-y-6", className)}>{children}</div>
    </div>
  );
};

const SettingTab = ({
  id,
  value,
  title,
  description,
  icon,
}: SettingTabProps) => {
  const { preferences, updatePreferences } = usePreference();

  // Get the current value for this setting
  const currentValue = preferences[id as keyof typeof preferences];
  const isSelected = currentValue === value;

  return (
    <Card
      className={cn(
        "w-full h-full cursor-pointer hover:bg-primary/5 ease-in-out duration-300",
        {
          "bg-primary/3": isSelected,
        }
      )}
      onClick={() => {
        if (id) {
          updatePreferences({
            [id]: value,
          });
        }
      }}
    >
      <CardContent className="flex flex-row items-start justify-between gap-4">
        <div className="flex flex-col gap-1">
          <h3 className="text-sm font-medium">{title}</h3>
          <p className="text-sm text-zinc-500">{description}</p>
        </div>
        {icon}
      </CardContent>
    </Card>
  );
};

const MiscellaneousSettings = () => {
  const {
    timezone,
    dateFormat,
    setPreferenceLanguage,
    setPreferenceTimezone,
    setPreferenceDateFormat,
  } = usePreference();

  return (
    <form action="" className="w-full flex flex-row items-center gap-6">
      <Select value={timezone} onValueChange={setPreferenceTimezone}>
        <SelectTrigger>
          <SelectValue placeholder="Select Timezone" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="UTC">UTC</SelectItem>
          <SelectItem value="America/New_York">Eastern Time</SelectItem>
          <SelectItem value="America/Chicago">Central Time</SelectItem>
          <SelectItem value="America/Denver">Mountain Time</SelectItem>
          <SelectItem value="America/Los_Angeles">Pacific Time</SelectItem>
          <SelectItem value="Europe/London">London</SelectItem>
          <SelectItem value="Europe/Paris">Paris</SelectItem>
          <SelectItem value="Asia/Tokyo">Tokyo</SelectItem>
        </SelectContent>
      </Select>

      <Select value={dateFormat} onValueChange={setPreferenceDateFormat}>
        <SelectTrigger>
          <SelectValue placeholder="Select Date Format" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="MM/DD/YYYY">MM/DD/YYYY</SelectItem>
          <SelectItem value="DD/MM/YYYY">DD/MM/YYYY</SelectItem>
          <SelectItem value="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
          <SelectItem value="DD MMM YYYY">DD MMM YYYY</SelectItem>
          <SelectItem value="MMM DD, YYYY">MMM DD, YYYY</SelectItem>
        </SelectContent>
      </Select>
    </form>
  );
};

interface SettingTabProps {
  value: string;
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
}
