import { CreditCard, AlertCircle } from "lucide-react";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/common/ui/card";

export const PaymentTab = () => {
  return (
    <Card className="bg-zinc-800 border-zinc-700">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <CreditCard className="w-5 h-5" />
          Payment Methods
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col items-center justify-center py-8 text-center">
          <AlertCircle className="w-12 h-12 text-zinc-500 mb-4" />
          <p className="text-zinc-400 mb-2">Payment methods not available</p>
          <p className="text-sm text-zinc-500">
            Payment management functionality has been removed
          </p>
        </div>
      </CardContent>
    </Card>
  );
};
