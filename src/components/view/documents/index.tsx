"use client";

import { useState, useEffect } from "react";
import { useParams, redirect } from "next/navigation";
import { useDocuments } from "@/hooks/useDocuments";
import { useLayout } from "@/hooks/useLayout";
import { Listing } from "@/layouts/dashboard/details";
import { DocumentHeader } from "./header";
import { DocumentStatistics as DocumentStatisticsComponent } from "./statistics";
import { DocumentCreateDialog } from "./create-dialog";
import { DocumentEditDialog } from "./details/edit-dialog";
import { DocumentDeleteDialog } from "./details/delete-dialog";
import { Button } from "@/components/common/ui/button";
import { RBACWrapper } from "@/components/common/rbac/RBACWrapper";
import { DEFAULT_ENTITIES, PERMISSION_ACTIONS } from "@/lib/rbac";
import { FileText } from "lucide-react";
import { LottieIconPlayer, LottieIconLib } from "@/components/common/lottie";
import type { Document as ApiDocument } from "@/lib/api/validators/schemas/document";
import type { Document } from "@/components/common/types";
import type { DataType } from "@/layouts/dashboard/details";
import type { InsightsWidget } from "@/layouts/dashboard/details/advanced";

// Adapter function to convert API document to UI document
const adaptApiDocumentToUI = (apiDocument: ApiDocument): Document => ({
  id: apiDocument.id,
  name: apiDocument.name,
  path: apiDocument.path,
  file_type: apiDocument.file_type,
  size: apiDocument.size,
  status: apiDocument.status as Document["status"],
  category: apiDocument.category,
  association_entity: apiDocument.association_entity,
  association_id: apiDocument.association_id,
  proposalId: apiDocument.proposalId,
  createdAt: new Date(apiDocument.createdAt).toISOString(),
  updatedAt: new Date(apiDocument.updatedAt).toISOString(),
});

export function DocumentContainer() {
  const { slug } = useParams();
  const { isAdvanced } = useLayout();

  const {
    documents,
    statistics,
    isLoading,
    isCreating,
    isUpdating,
    isDeleting,
    error,
    create,
    update,
    remove,
    downloadDocument,
    copyDocument,
    clearError,
  } = useDocuments();

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<ApiDocument | null>(
    null
  );
  const [searchTerm, setSearchTerm] = useState("");
  const [viewMode, setViewMode] = useState<"table" | "grid" | "list" | "flow">(
    "table"
  );

  useEffect(() => {
    if (!slug) {
      redirect("/");
    }
    // Data is automatically fetched by useSWR in the hook
    // No need for manual initialization
  }, [slug]);

  // useEffect(() => {
  //   if (error) {
  //     console.error("Document error:", error);
  //   }
  // }, [error]);

  // Clear error when component unmounts or when user interacts
  // useEffect(() => {
  //   return () => {
  //     clearError();
  //   };
  // }, [clearError]);

  const handleCreateDocument = async (documentData: FormData) => {
    try {
      await create(documentData);
      setIsCreateDialogOpen(false);
    } catch (error) {
      console.error("Failed to create document:", error);
    }
  };

  const handleUpdateDocument = async (id: string, documentData: any) => {
    try {
      await update(id, documentData);
    } catch (error) {
      console.error("Failed to update document:", error);
    }
  };

  const handleDeleteDocument = async (id: string) => {
    try {
      await remove(id);
    } catch (error) {
      console.error("Failed to delete document:", error);
    }
  };

  // Helper functions for dialog management
  const handleOpenDocument = (document: Document) => {
    // Navigate to document details or open in new tab
    window.open(`/${slug}/documents/${document.id}`, "_blank");
  };

  const handleEditDocumentDialog = (document: Document) => {
    // Convert MockDocument to Document for the dialog
    const apiDocument: ApiDocument = {
      id: document.id,
      name: document.name,
      path: document.path,
      file_type: document.file_type,
      size: document.size,
      status: document.status as Document["status"],
      category: document.category,
      association_entity: document.association_entity,
      association_id: document.association_id,
      proposalId: document.proposalId,
      createdAt: new Date(document.createdAt),
      updatedAt: new Date(document.updatedAt),
    };
    setSelectedDocument(apiDocument);
    setIsEditDialogOpen(true);
  };

  const handleEditSubmit = async (data: any) => {
    if (selectedDocument) {
      try {
        await update(selectedDocument.id, data);
        setIsEditDialogOpen(false);
        setSelectedDocument(null);
      } catch (error) {
        console.error("Failed to update document:", error);
      }
    }
  };

  const handleDeleteConfirm = async () => {
    if (selectedDocument) {
      try {
        await remove(selectedDocument.id);
        setIsDeleteDialogOpen(false);
        setSelectedDocument(null);
      } catch (error) {
        console.error("Failed to delete document:", error);
      }
    }
  };

  const handleDownloadDocument = async (document: Document) => {
    try {
      await downloadDocument(document);
    } catch (error) {
      console.error("Failed to download document:", error);
    }
  };

  const handleCopyDocument = async (document: Document) => {
    try {
      // Convert Document to ApiDocument for the API
      const apiDocument: ApiDocument = {
        id: document.id,
        name: document.name,
        path: document.path,
        file_type: document.file_type,
        size: document.size,
        status: document.status as Document["status"],
        category: document.category,
        association_entity: document.association_entity,
        association_id: document.association_id,
        proposalId: document.proposalId,
        createdAt: new Date(document.createdAt),
        updatedAt: new Date(document.updatedAt),
      };
      await copyDocument(apiDocument);
    } catch (error) {
      console.error("Failed to copy document:", error);
    }
  };

  // Convert API documents to UI documents
  const uiDocuments = documents.map(adaptApiDocumentToUI);

  // Filter documents based on search term
  const filteredDocuments = uiDocuments.filter(
    (doc) =>
      doc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doc.file_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doc.category?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Function to determine data type for dynamic icons (advanced layout)
  const getDataType = (item: Document, columnKey: string): DataType => {
    if (columnKey === "name") {
      const fileType = item.file_type.toLowerCase();
      if (
        fileType.includes("image") ||
        fileType.includes("jpg") ||
        fileType.includes("png") ||
        fileType.includes("gif")
      ) {
        return "image";
      } else if (
        fileType.includes("video") ||
        fileType.includes("mp4") ||
        fileType.includes("avi")
      ) {
        return "video";
      } else if (
        fileType.includes("audio") ||
        fileType.includes("mp3") ||
        fileType.includes("wav")
      ) {
        return "audio";
      } else if (
        fileType.includes("zip") ||
        fileType.includes("rar") ||
        fileType.includes("archive")
      ) {
        return "archive";
      } else if (
        fileType.includes("code") ||
        fileType.includes("js") ||
        fileType.includes("ts") ||
        fileType.includes("py")
      ) {
        return "code";
      } else if (
        fileType.includes("database") ||
        fileType.includes("sql") ||
        fileType.includes("db")
      ) {
        return "database";
      } else if (
        fileType.includes("pdf") ||
        fileType.includes("doc") ||
        fileType.includes("txt")
      ) {
        return "document";
      }
      return "file";
    }
    return "default";
  };

  // Quick filters for advanced layout
  const quickFilters = [
    {
      label: "All Documents",
      value: "all",
      count: uiDocuments.length,
      active: searchTerm === "",
      onClick: () => setSearchTerm(""),
    },
    {
      label: "Images",
      value: "images",
      count: uiDocuments.filter((doc) => getDataType(doc, "name") === "image")
        .length,
      active: false,
      onClick: () => setSearchTerm("image"),
    },
    {
      label: "Documents",
      value: "documents",
      count: uiDocuments.filter(
        (doc) => getDataType(doc, "name") === "document"
      ).length,
      active: false,
      onClick: () => setSearchTerm("pdf"),
    },
    {
      label: "Archives",
      value: "archives",
      count: uiDocuments.filter((doc) => getDataType(doc, "name") === "archive")
        .length,
      active: false,
      onClick: () => setSearchTerm("zip"),
    },
  ];

  // Insights widgets for advanced layout
  const insightsWidgets: InsightsWidget[] = [
    {
      id: "total-documents",
      title: "Total Documents",
      value: statistics?.totalDocuments || 0,
      description: "All documents in the system",
      icon: <FileText size={16} className="text-blue-500" />,
      trend: {
        value: 12,
        isPositive: true,
      },
    },
    {
      id: "recent-uploads",
      title: "Recent Uploads",
      value: statistics?.recentUploads || 0,
      description: "Documents uploaded this week",
      icon: <FileText size={16} className="text-green-500" />,
      trend: {
        value: 8,
        isPositive: true,
      },
    },
    {
      id: "storage-used",
      title: "Storage Used",
      value: statistics?.storageUsed || "0 MB",
      description: "Total storage consumed",
      icon: <FileText size={16} className="text-orange-500" />,
    },
    {
      id: "file-types",
      title: "File Types",
      value: new Set(uiDocuments.map((doc) => doc.file_type)).size,
      description: "Different file formats",
      icon: <FileText size={16} className="text-purple-500" />,
    },
  ];

  // Context menu actions for flow view
  const contextMenuActions = [
    {
      id: "open",
      label: "Open Document",
      icon: <LottieIconPlayer icon={LottieIconLib.view} size={16} />,
      onClick: (item: Document) => handleOpenDocument(item),
    },
    {
      id: "download",
      label: "Download",
      icon: <LottieIconPlayer icon={LottieIconLib.download} size={16} />,
      onClick: (item: Document) => handleDownloadDocument(item),
    },
    {
      id: "separator1",
      label: "",
      separator: true,
      onClick: () => {},
    },
    {
      id: "edit",
      label: "Edit",
      icon: <LottieIconPlayer icon={LottieIconLib.edit} size={16} />,
      onClick: (item: Document) => handleEditDocumentDialog(item),
    },
    {
      id: "copy",
      label: "Make a Copy",
      icon: <LottieIconPlayer icon={LottieIconLib.copy} size={16} />,
      onClick: (item: Document) => handleCopyDocument(item),
    },
    {
      id: "separator2",
      label: "",
      separator: true,
      onClick: () => {},
    },
    {
      id: "delete",
      label: "Delete",
      icon: (
        <LottieIconPlayer
          icon={LottieIconLib.delete}
          size={16}
          className="text-red-500"
        />
      ),
      onClick: (item: Document) => handleDeleteDocument(item.id),
    },
  ];

  // Table columns configuration
  const columns = [
    {
      key: "name",
      label: "Name",
      dataType: "file" as DataType,
    },
    {
      key: "file_type",
      label: "Type",
    },
    {
      key: "size",
      label: "Size",
      render: (item: Document) => {
        if (!item.size) return "-";
        const bytes = parseInt(item.size);
        if (bytes === 0) return "0 B";
        const k = 1024;
        const sizes = ["B", "KB", "MB", "GB"];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
      },
    },
    {
      key: "category",
      label: "Category",
    },
    {
      key: "createdAt",
      label: "Created",
      render: (item: Document) => new Date(item.createdAt).toLocaleDateString(),
    },
  ];

  return (
    <Listing
      className="space-y-3"
      toolbar={
        isAdvanced
          ? {
              searchValue: searchTerm,
              onSearchChange: setSearchTerm,
              searchPlaceholder: "Search documents...",
              quickFilters,
              viewMode,
              onViewModeChange: setViewMode,
              enabledViewModes: ["table", "grid", "flow"],
              showInsightsButton: true,
              insightsWidgets,
            }
          : undefined
      }
    >
      {/* Basic Layout Components */}
      {!isAdvanced && (
        <>
          <DocumentHeader
            onCreateDocument={() => setIsCreateDialogOpen(true)}
            documentsCount={documents.length}
          />

          <DocumentStatisticsComponent
            statistics={statistics}
            isLoading={isLoading}
          />
        </>
      )}

      {/* Advanced Layout Header */}
      {isAdvanced && (
        <Listing.Header
          title="Documents"
          caption={`Manage your project documents and files, ${
            documents.length
          } ${documents.length === 1 ? "document" : "documents"}`}
          actions={
            <RBACWrapper
              entity={DEFAULT_ENTITIES.DOCUMENT}
              action={PERMISSION_ACTIONS.CREATE}
            >
              <Button
                variant="secondary"
                onClick={() => setIsCreateDialogOpen(true)}
              >
                <LottieIconPlayer
                  icon={LottieIconLib.add}
                  size={16}
                  className="mr-2"
                />
                Upload New Document
              </Button>
            </RBACWrapper>
          }
        />
      )}

      {/* Table/Grid/Flow View */}
      {viewMode === "table" ? (
        <Listing.Table
          id="documents-table"
          data={filteredDocuments}
          columns={columns}
          loading={isLoading}
          getDataType={getDataType}
          contextMenuActions={contextMenuActions}
          emptyState={
            <div className="text-center py-12">
              <FileText className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                No documents
              </h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Get started by uploading your first document.
              </p>
            </div>
          }
        />
      ) : viewMode === "flow" ? (
        <Listing.Flow
          data={filteredDocuments}
          loading={isLoading}
          getDataType={(item) => getDataType(item, "name")}
          contextMenuActions={contextMenuActions}
          emptyState={
            <div className="text-center py-12">
              <FileText className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                No documents
              </h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Get started by uploading your first document.
              </p>
            </div>
          }
        />
      ) : (
        <Listing.Grid
          data={filteredDocuments}
          loading={isLoading}
          getDataType={(item) => getDataType(item, "name")}
          contextMenuActions={contextMenuActions}
          emptyState={
            <div className="text-center py-12">
              <FileText className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                No documents
              </h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Get started by uploading your first document.
              </p>
            </div>
          }
        />
      )}

      {/* Create Dialog */}
      <DocumentCreateDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onCreateDocument={handleCreateDocument}
        isLoading={isCreating}
      />

      {/* Edit Dialog */}
      {selectedDocument && (
        <DocumentEditDialog
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          document={selectedDocument}
          onUpdateDocument={handleEditSubmit}
          isLoading={isUpdating}
        />
      )}

      {/* Delete Dialog */}
      {selectedDocument && (
        <DocumentDeleteDialog
          open={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
          document={selectedDocument}
          onDeleteDocument={handleDeleteConfirm}
          isLoading={isDeleting}
        />
      )}
    </Listing>
  );
}
