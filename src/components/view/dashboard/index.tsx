"use client";

import { Tasks } from "./tasks";
import { Profile } from "./profile";
import { Summary } from "./summary";

export function DashboardContainer() {
  return (
    <div className="min-h-screen p-6 bg-background">
      {/* Main Grid */}
      <div className="grid grid-cols-12 gap-6">
        {/* Left Column - Profile */}
        <div className="col-span-4">
          <Profile />
        </div>
        {/* Right Column - Working Format, Calendar & Schedule */}
        <div className="col-span-8 h-screen overflow-y-scroll">
          <Summary />
        </div>
      </div>
    </div>
  );
}
