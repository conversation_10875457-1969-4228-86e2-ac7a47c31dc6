"use client";

import React, { Fragment } from "react";
import { Play, Video, Zap, Users } from "lucide-react";
import { But<PERSON> } from "@/components/common/ui/button";

import { Card, CardContent } from "@/components/common/ui/card";
import { ActivityData, TimeEntry } from "@/components/common/types";

// Types for better type safety
interface ActivityItemProps {
  label: string;
  percentage: number;
  icons?: React.ReactNode[];
  iconColor?: string;
}

interface TimeEntryItemProps {
  id: string;
  project: string;
  time: string;
  status: "active" | "completed";
}

// Local mock data (previously from dashboard-mock.ts)
const mockActivityData: ActivityData = {
  weeklyActivity: 72,
  design: 72,
  communication: 28,
};

const mockTimeEntries: TimeEntry[] = [
  {
    id: "1",
    project: "UX Researching",
    time: "01:34:07",
    status: "active",
  },
  {
    id: "2",
    project: "Wireframing",
    time: "02:15:30",
    status: "completed",
  },
];

// Radial Chart Decorative Lines Component
const RadialChartDecorations = () => {
  return (
    <svg className="absolute inset-0 w-full h-full" viewBox="0 0 200 200">
      {/* Radial lines */}
      {Array.from({ length: 60 }, (_, i) => {
        const angle = i * 6 * (Math.PI / 180);
        const x1 = 100 + Math.cos(angle) * 85;
        const y1 = 100 + Math.sin(angle) * 85;
        const x2 = 100 + Math.cos(angle) * 95;
        const y2 = 100 + Math.sin(angle) * 95;
        return (
          <line
            key={i}
            x1={x1}
            y1={y1}
            x2={x2}
            y2={y2}
            stroke="hsl(var(--muted-foreground))"
            strokeWidth="1"
          />
        );
      })}
    </svg>
  );
};

// Progress Circle Component
const ProgressCircle = ({ percentage }: { percentage: number }) => {
  return (
    <svg
      className="absolute inset-0 w-full h-full transform -rotate-90"
      viewBox="0 0 100 100"
    >
      <circle
        cx="50"
        cy="50"
        r="35"
        stroke="hsl(var(--border))"
        strokeWidth="3"
        fill="none"
      />
      <circle
        cx="50"
        cy="50"
        r="35"
        stroke="hsl(var(--primary))"
        strokeWidth="3"
        fill="none"
        strokeDasharray={`${percentage * 2.2} 220`}
        strokeLinecap="round"
      />
    </svg>
  );
};

// Chart Center Content Component
const ChartCenterContent = ({
  percentage,
  label,
}: {
  percentage: number;
  label: string;
}) => {
  return (
    <div className="absolute inset-0 flex flex-col items-center justify-center">
      <div className="text-4xl font-bold text-foreground">{percentage}%</div>
      <div className="text-sm text-muted-foreground">{label}</div>
    </div>
  );
};

// Radial Activity Chart Component
const RadialActivityChart = () => {
  return (
    <div className="flex items-center justify-center mb-6">
      <div className="relative w-48 h-48">
        <RadialChartDecorations />
        <ProgressCircle percentage={mockActivityData.weeklyActivity} />
        <ChartCenterContent
          percentage={mockActivityData.weeklyActivity}
          label="Weekly activity"
        />
      </div>
    </div>
  );
};

// Activity Item Component
const ActivityItem = ({
  label,
  percentage,
  icons,
  iconColor = "text-lime-500",
}: ActivityItemProps) => {
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-3">
        <div className="flex gap-1">
          {icons
            ? icons.map((icon, index) => (
                <div key={index} className={iconColor}>
                  {icon}
                </div>
              ))
            : Array.from({ length: 3 }, (_, i) => (
                <div key={i} className="w-4 h-4 bg-muted rounded"></div>
              ))}
        </div>
        <span className="text-foreground">{label}</span>
      </div>
      <span className="font-semibold text-foreground">{percentage}%</span>
    </div>
  );
};

// Activity List Component
const ActivityList = () => {
  const designIcons = [
    <Zap key="zap" className="w-4 h-4" />,
    <Users key="users" className="w-4 h-4" />,
    <Video key="video" className="w-4 h-4" />,
  ];

  return (
    <div className="space-y-4">
      <ActivityItem
        label="Design"
        percentage={mockActivityData.design}
        icons={designIcons}
        iconColor="text-lime-500"
      />
      <ActivityItem
        label="Communication"
        percentage={mockActivityData.communication}
      />
    </div>
  );
};

// Weekly Activity Card Component
const WeeklyActivityCard = () => {
  return (
    <Card className="bg-card border-border">
      <CardContent className="p-6">
        <RadialActivityChart />
        <ActivityList />
      </CardContent>
    </Card>
  );
};

// Time Tracking Header Component
const TimeTrackingHeader = ({
  title,
  subtitle,
}: {
  title: string;
  subtitle: string;
}) => {
  return (
    <div className="flex items-center justify-between mb-6">
      <h3 className="text-lg font-semibold text-foreground">{title}</h3>
      <div className="text-sm text-muted-foreground">{subtitle}</div>
    </div>
  );
};

// Current Time Display Component
const CurrentTimeDisplay = ({ time }: { time: string }) => {
  return (
    <div className="text-center mb-6">
      <div className="text-3xl font-mono font-bold mb-2 text-foreground">
        {time}
      </div>
      <Button
        variant="ghost"
        size="sm"
        className="text-primary hover:bg-primary/10"
      >
        <Play className="w-4 h-4" />
      </Button>
    </div>
  );
};

// More Options Button Component
const MoreOptionsButton = () => {
  return (
    <Button
      variant="ghost"
      size="sm"
      className="text-muted-foreground hover:text-foreground"
    >
      <div className="flex gap-1">
        <div className="w-1 h-1 bg-current rounded-full"></div>
        <div className="w-1 h-1 bg-current rounded-full"></div>
        <div className="w-1 h-1 bg-current rounded-full"></div>
      </div>
    </Button>
  );
};

// Time Entry Item Component
const TimeEntryItem = ({ entry }: { entry: TimeEntryItemProps }) => {
  return (
    <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
      <div className="flex items-center gap-3">
        <div className="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center">
          <Zap className="w-4 h-4 text-yellow-900" />
        </div>
        <span className="text-foreground">{entry.project}</span>
      </div>
      <div className="flex items-center gap-2">
        <span className="font-mono text-sm text-foreground">{entry.time}</span>
        <MoreOptionsButton />
      </div>
    </div>
  );
};

// Time Entries List Component
const TimeEntriesList = () => {
  return (
    <div className="space-y-3">
      {mockTimeEntries.map((entry) => (
        <TimeEntryItem key={entry.id} entry={entry} />
      ))}
    </div>
  );
};

// Time Tracking Card Component
const TimeTrackingCard = () => {
  return (
    <Card className="bg-card border-border">
      <CardContent className="p-6">
        <TimeTrackingHeader title="Time tracking" subtitle="Travel app" />
        <CurrentTimeDisplay time="02:17:51" />
        <TimeEntriesList />
      </CardContent>
    </Card>
  );
};

// Main Tasks Component using compound pattern
export const Tasks = () => {
  return (
    <Fragment>
      <WeeklyActivityCard />
    </Fragment>
  );
};

// Export sub-components for potential reuse
Tasks.WeeklyActivity = WeeklyActivityCard;
Tasks.RadialChart = RadialActivityChart;
Tasks.ActivityList = ActivityList;
Tasks.ActivityItem = ActivityItem;
Tasks.TimeTracking = TimeTrackingCard;
Tasks.TimeEntry = TimeEntryItem;
Tasks.TimeEntriesList = TimeEntriesList;
Tasks.CurrentTimeDisplay = CurrentTimeDisplay;
