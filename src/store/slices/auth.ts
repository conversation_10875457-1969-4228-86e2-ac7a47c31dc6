import { Session } from "next-auth";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { updateUserAvatar, updateUserProfile } from "../actions/auth";

export interface User extends Session {
  user: Session["user"] & {
    customerId: string;
    role: Role;
    profile: Profile;
    account: { id: string };
    createdAt: Date;
    updatedAt: Date;
  };
}

export interface Profile {
  id: string;
  about: string;
  website: string;
  socials: string[];
  location: string;
}

export interface Role {
  id: string;
  name: string;
  description: string;
  status: string;
  permissions: string[];
}

// Define the auth state interface
export interface AuthState {
  session: Session | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: any;
  isDashboardView: boolean;
  user: {
    id?: string;
    name?: string | null;
    customerId: string;
    email?: string | null;
    image?: string | null;
    role: Role;
    profile: Profile;
    account: { id: string };
    createdAt: Date;
    updatedAt: Date;
  } | null;
}

// Initial state
const initialState: AuthState = {
  session: null,
  isAuthenticated: false,
  isLoading: true,
  error: null,
  isDashboardView: false,
  user: null,
};

// Create the auth slice
const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    // Set session data from next-auth
    setSession: (state, action: PayloadAction<User | any>) => {
      state.session = action.payload;
      state.isAuthenticated = !!action.payload;
      state.isLoading = false;

      if (action?.payload?.user) {
        state.user = action.payload.user;
      }
    },

    // Set loading state
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },

    // Clear session (logout)
    clearSession: (state) => {
      state.session = null;
      state.isAuthenticated = false;
      state.isLoading = false;
      state.user = null;
    },

    // Update user profile data with recursive safe assignment
    updateUser: (state, action: PayloadAction<Partial<AuthState["user"]>>) => {
      // Use safe user update utility for comprehensive validation
      state.user = action.payload;
    },

    // Set dashboard view state
    setDashboardView: (state, action: PayloadAction<boolean>) => {
      state.isDashboardView = action.payload;
    },

    // Toggle dashboard view state
    toggleDashboardView: (state) => {
      state.isDashboardView = !state.isDashboardView;
    },
  },
  extraReducers: (builder) => {
    // Upsert user profile (create or update)
    builder
      .addCase(updateUserProfile.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateUserProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        if (state.user && action.payload) {
          // Use safe assignment for profile update
          state.user.profile = action.payload;
        }
      })
      .addCase(updateUserProfile.rejected, (state) => {
        state.isLoading = false;
      });

    builder
      .addCase(updateUserAvatar.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateUserAvatar.fulfilled, (state, action) => {
        state.isLoading = false;
        if (state.user && action.payload?.image !== undefined) {
          // Use safe assignment for image update
          state.user.image = action.payload.image;
        }
      })
      .addCase(updateUserAvatar.rejected, (state) => {
        state.isLoading = false;
      });
  },
});

// Export actions
export const {
  setSession,
  setLoading,
  clearSession,
  updateUser,
  setDashboardView,
  toggleDashboardView,
} = authSlice.actions;

// Export selectors
export const selectAuth = (state: { auth: AuthState }) => state.auth;
export const selectRole = (state: { auth: AuthState }) => state.auth.user?.role;
export const selectSessionAccount = (state: { auth: AuthState }) =>
  (state.auth?.session?.user as any)?.account;
export const selectPermissions = (state: { auth: AuthState }) =>
  state.auth.user?.role?.permissions;
export const selectUserAccount = (state: { auth: AuthState }) =>
  state.auth?.user?.account;
export const selectProfile = (state: { auth: AuthState }) =>
  state.auth.user?.profile;
export const selectPersonlizedRoute = (state: { auth: AuthState }) =>
  state.auth.user?.name?.trim().replace(/\s+/g, "-").toLowerCase();
export const selectSession = (state: { auth: AuthState }) => state.auth.session;
export const selectUser = (state: { auth: AuthState }) => state.auth.user;
export const selectIsAuthenticated = (state: { auth: AuthState }) =>
  state.auth.isAuthenticated;
export const selectIsLoading = (state: { auth: AuthState }) =>
  state.auth.isLoading;
export const selectIsDashboardView = (state: { auth: AuthState }) =>
  state.auth.isDashboardView;

// Export reducer
export default authSlice;
