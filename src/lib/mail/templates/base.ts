/**
 * Base email template with Underscore International branding
 * Uses the website's color palette from globals.css
 */

// Color palette from globals.css lines 8-14
export const COLORS = {
  neon: '#00FF99',
  navy: '#051928',
  white: '#ffffffde',
  black: '#000000',
  gray: '#6b7280',
  lightGray: '#f3f4f6',
  darkGray: '#374151',
} as const;

export interface BaseTemplateProps {
  title: string;
  preheader?: string;
  content: string;
}

/**
 * Generate base email template
 */
export function generateBaseTemplate({ title, preheader, content }: BaseTemplateProps): string {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>${title}</title>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
    <style>
        /* Reset styles */
        body, table, td, p, a, li, blockquote {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        table, td {
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        img {
            -ms-interpolation-mode: bicubic;
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
        }
        
        /* Base styles */
        body {
            margin: 0 !important;
            padding: 0 !important;
            background-color: ${COLORS.lightGray};
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            font-size: 16px;
            line-height: 1.6;
            color: ${COLORS.darkGray};
        }
        
        /* Container */
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: ${COLORS.white};
        }
        
        /* Header */
        .header {
            background: linear-gradient(135deg, ${COLORS.navy} 0%, #0a2540 100%);
            padding: 40px 30px;
            text-align: center;
        }
        
        .logo {
            color: ${COLORS.neon};
            font-size: 28px;
            font-weight: bold;
            text-decoration: none;
            letter-spacing: -0.5px;
        }
        
        .tagline {
            color: ${COLORS.white};
            font-size: 14px;
            margin-top: 8px;
            opacity: 0.9;
        }
        
        /* Content */
        .content {
            padding: 40px 30px;
        }
        
        .content h1 {
            color: ${COLORS.navy};
            font-size: 24px;
            font-weight: 600;
            margin: 0 0 20px 0;
            line-height: 1.3;
        }
        
        .content h2 {
            color: ${COLORS.navy};
            font-size: 20px;
            font-weight: 600;
            margin: 30px 0 15px 0;
            line-height: 1.3;
        }
        
        .content p {
            margin: 0 0 16px 0;
            color: ${COLORS.darkGray};
        }
        
        /* Buttons */
        .button {
            display: inline-block;
            padding: 14px 28px;
            background-color: ${COLORS.neon};
            color: ${COLORS.navy} !important;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            transition: all 0.2s ease;
            margin: 20px 0;
        }
        
        .button:hover {
            background-color: #00e68a;
            transform: translateY(-1px);
        }
        
        .button-secondary {
            background-color: transparent;
            color: ${COLORS.navy} !important;
            border: 2px solid ${COLORS.navy};
        }
        
        .button-secondary:hover {
            background-color: ${COLORS.navy};
            color: ${COLORS.white} !important;
        }
        
        /* Cards */
        .card {
            background-color: ${COLORS.lightGray};
            border-radius: 8px;
            padding: 24px;
            margin: 20px 0;
            border-left: 4px solid ${COLORS.neon};
        }
        
        /* Footer */
        .footer {
            background-color: ${COLORS.navy};
            color: ${COLORS.white};
            padding: 30px;
            text-align: center;
            font-size: 14px;
        }
        
        .footer a {
            color: ${COLORS.neon};
            text-decoration: none;
        }
        
        .footer a:hover {
            text-decoration: underline;
        }
        
        .social-links {
            margin: 20px 0;
        }
        
        .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: ${COLORS.white};
            text-decoration: none;
        }
        
        /* Responsive */
        @media only screen and (max-width: 600px) {
            .email-container {
                width: 100% !important;
            }
            
            .header, .content, .footer {
                padding: 20px !important;
            }
            
            .content h1 {
                font-size: 20px !important;
            }
            
            .button {
                display: block !important;
                width: 100% !important;
                box-sizing: border-box;
            }
        }
    </style>
</head>
<body>
    ${preheader ? `
    <!-- Preheader text -->
    <div style="display: none; font-size: 1px; color: #fefefe; line-height: 1px; font-family: sans-serif; max-height: 0px; max-width: 0px; opacity: 0; overflow: hidden;">
        ${preheader}
    </div>
    ` : ''}
    
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <a href="${process.env.NEXT_PUBLIC_APP_URL || 'https://underscor.io'}" class="logo">
                underscore
            </a>
            <div class="tagline">International Software Solutions</div>
        </div>
        
        <!-- Content -->
        <div class="content">
            ${content}
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <p>
                <strong>Underscore International Company Limited</strong><br>
                Professional Software Development & Consulting
            </p>
            
            <div class="social-links">
                <a href="mailto:<EMAIL>">Contact Us</a>
                <a href="${process.env.NEXT_PUBLIC_APP_URL || 'https://underscor.io'}">Visit Website</a>
            </div>
            
            <p style="font-size: 12px; opacity: 0.8; margin-top: 20px;">
                © ${new Date().getFullYear()} Underscore International Company Limited. All rights reserved.<br>
                You received this email because you have an account with us.
            </p>
        </div>
    </div>
</body>
</html>
  `.trim();
}

/**
 * Utility function to format currency
 */
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
}

/**
 * Utility function to format date
 */
export function formatDate(date: string | Date): string {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(new Date(date));
}
