import { generateBaseTemplate, COLORS } from './base';

export interface PasswordResetTemplateProps {
  userName: string;
  resetUrl: string;
}

/**
 * Generate password reset email template
 */
export function generatePasswordResetTemplate({
  userName,
  resetUrl,
}: PasswordResetTemplateProps): string {
  const content = `
    <h1>Password Reset Request</h1>
    
    <p>Hi ${userName},</p>
    
    <p>We received a request to reset the password for your Underscore International account. If you made this request, click the button below to reset your password.</p>
    
    <div style="text-align: center; margin: 30px 0;">
        <a href="${resetUrl}" class="button">
            Reset Your Password
        </a>
    </div>
    
    <div class="card" style="background-color: #f59e0b20; border-left: 4px solid #f59e0b;">
        <h2 style="color: #f59e0b; margin-top: 0;">⏰ Important</h2>
        <p style="margin-bottom: 0;">
            This password reset link will expire in <strong>24 hours</strong> for security reasons. If you need a new link after it expires, you can request another password reset.
        </p>
    </div>
    
    <h2>Security Information</h2>
    
    <div class="card">
        <h3 style="margin-top: 0; color: ${COLORS.navy};">🔒 For Your Security</h3>
        <ul style="margin: 16px 0; padding-left: 20px;">
            <li>This link can only be used once</li>
            <li>It will expire in 24 hours</li>
            <li>Only you should have access to this email</li>
            <li>Never share this link with anyone</li>
        </ul>
    </div>
    
    <h2>Didn't Request This?</h2>
    <p>If you didn't request a password reset, you can safely ignore this email. Your password will remain unchanged, and no further action is required.</p>
    
    <div class="card" style="background-color: ${COLORS.navy}; color: ${COLORS.white}; border-left: 4px solid ${COLORS.neon};">
        <h3 style="color: ${COLORS.neon}; margin-top: 0;">🛡️ Account Security Tips</h3>
        <ul style="color: ${COLORS.white}; margin: 16px 0; padding-left: 20px;">
            <li>Use a strong, unique password</li>
            <li>Enable two-factor authentication if available</li>
            <li>Never share your login credentials</li>
            <li>Log out from shared or public computers</li>
        </ul>
        <p style="color: ${COLORS.white}; margin-bottom: 0;">
            <strong>Remember:</strong> We'll never ask for your password via email or phone.
        </p>
    </div>
    
    <h2>Alternative Method</h2>
    <p>If the button above doesn't work, you can copy and paste this link into your browser:</p>
    
    <div style="
        background-color: ${COLORS.lightGray};
        padding: 16px;
        border-radius: 6px;
        border: 1px solid #d1d5db;
        margin: 16px 0;
        word-break: break-all;
        font-family: monospace;
        font-size: 14px;
        color: ${COLORS.darkGray};
    ">
        ${resetUrl}
    </div>
    
    <h2>Need Help?</h2>
    <p>If you're having trouble resetting your password or have any security concerns, please contact our support team:</p>
    
    <ul style="margin: 16px 0; padding-left: 20px;">
        <li><strong>Email:</strong> <a href="mailto:<EMAIL>" style="color: ${COLORS.neon};"><EMAIL></a></li>
        <li><strong>Subject:</strong> Password Reset Assistance</li>
        <li><strong>Include:</strong> Your username or email address</li>
    </ul>
    
    <p>Our security team is available 24/7 to help you regain access to your account safely.</p>
    
    <p style="margin-top: 30px;">
        Best regards,<br>
        <strong>The Underscore International Security Team</strong>
    </p>
  `;

  return generateBaseTemplate({
    title: 'Password Reset Request',
    preheader: `Reset your password for your Underscore International account.`,
    content,
  });
}
