"use client";

/**
 * Unified Notification Library
 *
 * This library combines email, in-app (toast), and push notifications
 * based on user preferences and notification contexts.
 *
 * Usage: notify(email, inApp, push)
 * Example: notify(true, true, false) - sends email and in-app, but not push
 */

import { toast } from "sonner";

// Import notification services
import { serviceWorkerManager } from "@/lib/service-worker";
import { api } from "@/lib/common/requests";

// Notification types that map to the preferences structure
export type NotificationType =
  | "room"
  | "chat"
  | "proposal"
  | "contract"
  | "systemAlerts"
  | "roleChanges"
  | "weeklyDigest";

// Notification channels
export interface NotificationChannels {
  email: boolean;
  inApp: boolean;
  push: boolean;
}

// Base notification payload
export interface NotificationPayload {
  type: NotificationType;
  title: string;
  message: string;
  data?: {
    entityId?: string;
    entityType?: string;
    actionUrl?: string;
    userId?: string;
    userEmail?: string;
    userName?: string;
    [key: string]: any;
  };
  // Email-specific data
  emailData?: {
    to: string;
    templateData?: any;
    customTemplate?: string;
  };
  // Push-specific data
  pushData?: {
    icon?: string;
    badge?: string;
    image?: string;
    tag?: string;
    actions?: Array<{
      action: string;
      title: string;
      icon?: string;
    }>;
    requireInteraction?: boolean;
    silent?: boolean;
  };
  // In-app specific data
  inAppData?: {
    duration?: number;
    action?: {
      label: string;
      onClick: () => void;
    };
    description?: string;
    variant?: "default" | "destructive" | "success" | "warning" | "info";
  };
}

// CRUD operations interface
export interface CRUDOperations {
  create: boolean;
  update: boolean;
  delete: boolean;
}

// Notification preferences context (from useNotifications hook)
export interface NotificationPreferences {
  email: {
    room: CRUDOperations;
    chat: CRUDOperations;
    proposal: CRUDOperations;
    contract: CRUDOperations;
    systemAlerts: boolean;
    weeklyDigest: boolean;
  };
  push: {
    room: CRUDOperations;
    chat: CRUDOperations;
    proposal: CRUDOperations;
    contract: CRUDOperations;
    systemAlerts: boolean;
  };
  inApp: {
    room: CRUDOperations;
    chat: CRUDOperations;
    proposal: CRUDOperations;
    contract: CRUDOperations;
    systemAlerts: boolean;
    roleChanges: boolean;
  };
}

class NotificationManager {
  private preferences: NotificationPreferences | null = null;

  /**
   * Set user notification preferences
   */
  setPreferences(preferences: NotificationPreferences) {
    this.preferences = preferences;
  }

  /**
   * Get current user notification preferences
   */
  getPreferences(): NotificationPreferences | null {
    return this.preferences;
  }

  /**
   * Get notification channels based on user preferences and override
   * For CRUD-based notifications, we check if any CRUD operation is enabled
   */
  private getNotificationChannels(
    type: NotificationType,
    context?: string,
    operation?: keyof CRUDOperations,
    override?: Partial<NotificationChannels>
  ): NotificationChannels {
    const defaultChannels: NotificationChannels = {
      email: false,
      inApp: false,
      push: false,
    };

    if (!this.preferences) {
      return override ? { ...defaultChannels, ...override } : defaultChannels;
    }

    const channels: NotificationChannels = { ...defaultChannels };

    // Handle context-based CRUD operations
    if (context) {
      const emailContext = (this.preferences.email as any)[context];
      const pushContext = (this.preferences.push as any)[context];
      const inAppContext = (this.preferences.inApp as any)[context];

      if (operation) {
        // Check specific operation
        channels.email = emailContext?.[operation] || false;
        channels.push = pushContext?.[operation] || false;
        channels.inApp = inAppContext?.[operation] || false;
      } else {
        // Check if any CRUD operation is enabled for the context
        channels.email =
          emailContext &&
          (emailContext.create || emailContext.update || emailContext.delete);
        channels.push =
          pushContext &&
          (pushContext.create || pushContext.update || pushContext.delete);
        channels.inApp =
          inAppContext &&
          (inAppContext.create || inAppContext.update || inAppContext.delete);
      }
    } else {
      // Handle direct boolean properties (systemAlerts, weeklyDigest, roleChanges)
      const emailSetting = (this.preferences.email as any)[type];
      const pushSetting = (this.preferences.push as any)[type];
      const inAppSetting = (this.preferences.inApp as any)[type];

      channels.email = typeof emailSetting === "boolean" ? emailSetting : false;
      channels.push = typeof pushSetting === "boolean" ? pushSetting : false;
      channels.inApp = typeof inAppSetting === "boolean" ? inAppSetting : false;
    }

    // Apply overrides if provided
    if (override) {
      return { ...channels, ...override };
    }

    return channels;
  }

  /**
   * Send generic notification email using server-side API
   */
  private async sendGenericNotificationEmail(
    to: string,
    data: {
      title: string;
      message: string;
      type: string;
      actionUrl?: string;
      priority?: string;
      [key: string]: any;
    }
  ): Promise<void> {
    try {
      // Send email via server-side API using common requests utility
      await api.post("notifications/send-email", {
        to,
        type: data.type,
        title: data.title,
        message: data.message,
        actionUrl: data.actionUrl,
        priority: data.priority,
        templateData: data,
      });

      console.log(`✅ Generic email sent to ${to} for ${data.type}`);
    } catch (error) {
      console.error("Failed to send generic notification email:", error);
      throw error;
    }
  }

  /**
   * Send contract notification email using server-side API
   */
  private async sendContractNotificationEmail(
    to: string,
    contractData: any
  ): Promise<void> {
    try {
      // Send contract email via server-side API using common requests utility
      await api.post("notifications/send-contract-email", {
        to,
        contractData,
      });

      console.log(`✅ Contract email sent to ${to}`);
    } catch (error) {
      console.error("Failed to send contract notification email:", error);
      throw error;
    }
  }

  /**
   * Send email notification
   */
  private async sendEmailNotification(
    payload: NotificationPayload
  ): Promise<boolean> {
    try {
      console.log("📧 Attempting to send email notification:", {
        type: payload.type,
        title: payload.title,
        to: payload.emailData?.to,
      });

      if (!payload.emailData?.to) {
        console.warn("❌ Email notification requires 'to' address");
        return false;
      }

      const { to, templateData } = payload.emailData;

      // Route to appropriate email template based on notification type
      switch (payload.type) {
        case "chat":
          await this.sendGenericNotificationEmail(to, {
            title: payload.title,
            message: payload.message,
            type: "Chat Message",
            actionUrl: templateData?.actionUrl,
            ...templateData,
          });
          break;

        case "contract":
          if (templateData) {
            // Send contract-specific email via API
            await this.sendContractNotificationEmail(to, templateData);
          } else {
            await this.sendGenericNotificationEmail(to, {
              title: payload.title,
              message: payload.message,
              type: "Contract Update",
              actionUrl: templateData?.actionUrl,
            });
          }
          break;

        case "proposal":
          await this.sendGenericNotificationEmail(to, {
            title: payload.title,
            message: payload.message,
            type: "Proposal Update",
            actionUrl: templateData?.actionUrl,
            ...templateData,
          });
          break;

        case "room":
          await this.sendGenericNotificationEmail(to, {
            title: payload.title,
            message: payload.message,
            type: "Room Activity",
            actionUrl: templateData?.actionUrl,
            ...templateData,
          });
          break;

        case "systemAlerts":
          await this.sendGenericNotificationEmail(to, {
            title: payload.title,
            message: payload.message,
            type: "System Alert",
            actionUrl:
              templateData?.actionUrl || "/dashboard/settings/notifications",
            priority: "high",
            ...templateData,
          });
          break;

        case "roleChanges":
          await this.sendGenericNotificationEmail(to, {
            title: payload.title,
            message: payload.message,
            type: "Role Change",
            actionUrl: templateData?.actionUrl || "/dashboard/settings/account",
            priority: "high",
            ...templateData,
          });
          break;

        case "weeklyDigest":
          await this.sendGenericNotificationEmail(to, {
            title: payload.title,
            message: payload.message,
            type: "Weekly Digest",
            actionUrl: templateData?.actionUrl || "/dashboard",
            ...templateData,
          });
          break;

        default:
          console.warn(
            `Email notification for ${payload.type} not implemented, using generic template`
          );
          await this.sendGenericNotificationEmail(to, {
            title: payload.title,
            message: payload.message,
            type: "Notification",
            actionUrl: templateData?.actionUrl,
          });
          break;
      }

      return true;
    } catch (error) {
      console.error("Failed to send email notification:", error);
      return false;
    }
  }

  /**
   * Send in-app (toast) notification
   */
  private async sendInAppNotification(
    payload: NotificationPayload
  ): Promise<boolean> {
    try {
      const { title, message, inAppData } = payload;
      const variant = inAppData?.variant || "default";
      const duration = inAppData?.duration || 5000;

      // Map variants to toast methods
      switch (variant) {
        case "success":
          toast.success(title, {
            description: message,
            duration,
            action: inAppData?.action,
          });
          break;

        case "destructive":
          toast.error(title, {
            description: message,
            duration,
            action: inAppData?.action,
          });
          break;

        case "warning":
          toast.warning(title, {
            description: message,
            duration,
            action: inAppData?.action,
          });
          break;

        case "info":
          toast.info(title, {
            description: message,
            duration,
            action: inAppData?.action,
          });
          break;

        default:
          toast(title, {
            description: message,
            duration,
            action: inAppData?.action,
          });
          break;
      }

      return true;
    } catch (error) {
      console.error("Failed to send in-app notification:", error);
      return false;
    }
  }

  /**
   * Diagnose push notification setup
   */
  private async diagnosePushNotificationSetup(): Promise<{
    canSendPush: boolean;
    issues: string[];
    details: {
      serviceWorkerSupported: boolean;
      serviceWorkerRegistered: boolean;
      notificationPermission: NotificationPermission;
      pushManagerSupported: boolean;
      hasSubscription: boolean;
    };
  }> {
    const issues: string[] = [];
    const details = {
      serviceWorkerSupported: false,
      serviceWorkerRegistered: false,
      notificationPermission: "default" as NotificationPermission,
      pushManagerSupported: false,
      hasSubscription: false,
    };

    try {
      // Check service worker support
      details.serviceWorkerSupported = "serviceWorker" in navigator;
      if (!details.serviceWorkerSupported) {
        issues.push("Service Worker not supported");
      }

      // Check notification support
      if (!("Notification" in window)) {
        issues.push("Notifications not supported");
      } else {
        details.notificationPermission = Notification.permission;
        if (details.notificationPermission !== "granted") {
          issues.push(
            `Notification permission: ${details.notificationPermission}`
          );
        }
      }

      // Check push manager support
      details.pushManagerSupported = "PushManager" in window;
      if (!details.pushManagerSupported) {
        issues.push("Push Manager not supported");
      }

      // Check service worker registration
      if (details.serviceWorkerSupported) {
        try {
          const registration = await navigator.serviceWorker.getRegistration();
          details.serviceWorkerRegistered = !!registration;
          if (!details.serviceWorkerRegistered) {
            issues.push("Service Worker not registered");
          } else if (registration && registration.pushManager) {
            // Check push subscription
            try {
              const subscription =
                await registration.pushManager.getSubscription();
              details.hasSubscription = !!subscription;
              if (!details.hasSubscription) {
                issues.push("No push subscription found");
              }
            } catch (subError) {
              issues.push("Failed to check push subscription");
            }
          }
        } catch (regError) {
          issues.push("Failed to check service worker registration");
        }
      }

      const canSendPush =
        details.serviceWorkerSupported &&
        details.serviceWorkerRegistered &&
        details.notificationPermission === "granted" &&
        details.pushManagerSupported;

      return {
        canSendPush,
        issues,
        details,
      };
    } catch (error) {
      issues.push(
        `Diagnostic error: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
      return {
        canSendPush: false,
        issues,
        details,
      };
    }
  }

  /**
   * Send push notification
   */
  private async sendPushNotification(
    payload: NotificationPayload
  ): Promise<boolean> {
    try {
      console.log("🔔 Attempting to send push notification:", {
        type: payload.type,
        title: payload.title,
        userId: payload.data?.userId,
        hasWindow: typeof window !== "undefined",
      });

      // Check push notification prerequisites
      if (typeof window !== "undefined") {
        const diagnostics = await this.diagnosePushNotificationSetup();
        console.log("🔍 Push notification diagnostics:", diagnostics);

        if (!diagnostics.canSendPush) {
          console.warn(
            "❌ Push notifications not available:",
            diagnostics.issues
          );
          return false;
        }
      }

      // For client-side push notifications, we use the service worker
      if (typeof window !== "undefined") {
        console.log("🔔 Attempting local notification via service worker");
        try {
          await serviceWorkerManager.showLocalNotification({
            title: payload.title,
            body: payload.message,
            icon: payload.pushData?.icon,
            badge: payload.pushData?.badge,
            image: payload.pushData?.image,
            tag: payload.pushData?.tag || `${payload.type}-${Date.now()}`,
            data: payload.data,
            actions: payload.pushData?.actions,
            requireInteraction: payload.pushData?.requireInteraction,
            silent: payload.pushData?.silent,
          });
          console.log("✅ Local notification sent successfully");
        } catch (localError) {
          console.error("❌ Failed to send local notification:", localError);
        }
      }

      // For server-side push notifications, we need a userId
      if (!payload.data?.userId) {
        console.warn(
          "Push notification skipped: missing userId in payload.data"
        );
        return false;
      }

      try {
        console.log("🔔 Attempting server-side push notification via API");
        const pushPayload = {
          userId: payload.data.userId,
          payload: {
            title: payload.title,
            body: payload.message,
            icon:
              payload.pushData?.icon || "/favicons/android-chrome-192x192.png",
            badge: payload.pushData?.badge || "/favicons/favicon-32x32.png",
            image: payload.pushData?.image,
            tag: payload.pushData?.tag || `notification-${Date.now()}`,
            data: {
              ...payload.data,
              notificationId:
                payload.data.notificationId || `notif-${Date.now()}`,
              timestamp: Date.now(),
            },
            actions: payload.pushData?.actions || [
              {
                action: "view",
                title: "View",
                icon: "/favicons/favicon-16x16.png",
              },
              {
                action: "dismiss",
                title: "Dismiss",
              },
            ],
            requireInteraction: payload.pushData?.requireInteraction || false,
            silent: payload.pushData?.silent || false,
          },
        };

        console.log("🔔 Push payload:", pushPayload);

        const response = await api.post("push/send", pushPayload);
        console.log(
          "✅ Server-side push notification sent successfully:",
          response
        );

        return true;
      } catch (error) {
        console.error("Failed to send push notification:", error);
        return false;
      }
    } catch (error) {
      console.error("Failed to send push notification:", error);
      return false;
    }
  }

  /**
   * Main notification method - sends notifications based on preferences and overrides
   */
  async notify(
    payload: NotificationPayload,
    context?: string,
    operation?: keyof CRUDOperations,
    channelOverride?: Partial<NotificationChannels>
  ): Promise<{
    success: boolean;
    results: {
      email: boolean;
      inApp: boolean;
      push: boolean;
    };
  }> {
    const channels = this.getNotificationChannels(
      payload.type,
      context,
      operation,
      channelOverride
    );

    console.log("🔔 Notification channels determined:", {
      type: payload.type,
      context,
      operation,
      channels,
      channelOverride,
      hasPreferences: !!this.preferences,
    });

    const results = {
      email: false,
      inApp: false,
      push: false,
    };

    // Send notifications based on enabled channels
    const promises: Promise<void>[] = [];

    if (channels.email) {
      promises.push(
        this.sendEmailNotification(payload).then((success) => {
          results.email = success;
        })
      );
    }

    if (channels.inApp) {
      promises.push(
        this.sendInAppNotification(payload).then((success) => {
          results.inApp = success;
        })
      );
    }

    if (channels.push) {
      promises.push(
        this.sendPushNotification(payload).then((success) => {
          results.push = success;
        })
      );
    }

    // Wait for all notifications to complete
    await Promise.all(promises);

    const success = Object.values(results).some((result) => result);

    console.log("✅ Notification sending completed:", {
      type: payload.type,
      results,
      success,
      channelsAttempted: {
        email: channels.email,
        inApp: channels.inApp,
        push: channels.push,
      },
    });

    return { success, results };
  }

  /**
   * Convenience method for simple notifications with channel override
   */
  async notifyWithChannels(
    type: NotificationType,
    title: string,
    message: string,
    email: boolean = false,
    inApp: boolean = true,
    push: boolean = false,
    additionalData?: Partial<NotificationPayload>
  ) {
    const payload: NotificationPayload = {
      type,
      title,
      message,
      ...additionalData,
    };

    return this.notify(payload, undefined, undefined, { email, inApp, push });
  }
}

// Create singleton instance
export const notificationManager = new NotificationManager();

// Convenience function for the main notify method
export async function notify(
  payload: NotificationPayload,
  operation?: keyof CRUDOperations,
  channelOverride?: Partial<NotificationChannels>
) {
  // For context-based notifications, use the payload.type as the context
  const context = ["room", "chat", "proposal", "contract"].includes(
    payload.type
  )
    ? payload.type
    : undefined;

  return notificationManager.notify(
    payload,
    context,
    operation,
    channelOverride
  );
}

// Convenience function with explicit channel parameters
export async function notifyWithChannels(
  type: NotificationType,
  title: string,
  message: string,
  email: boolean = false,
  inApp: boolean = true,
  push: boolean = false,
  additionalData?: Partial<NotificationPayload>
) {
  return notificationManager.notifyWithChannels(
    type,
    title,
    message,
    email,
    inApp,
    push,
    additionalData
  );
}

// React hook for integrated notification management
export function useNotificationManager() {
  // This would typically import useNotifications from the hook
  // For now, we'll provide a basic implementation

  const setUserPreferences = (preferences: NotificationPreferences) => {
    notificationManager.setPreferences(preferences);
  };

  const sendNotification = async (
    payload: NotificationPayload,
    context?: string,
    operation?: keyof CRUDOperations,
    channelOverride?: Partial<NotificationChannels>
  ) => {
    return notificationManager.notify(
      payload,
      context,
      operation,
      channelOverride
    );
  };

  const sendSimpleNotification = async (
    type: NotificationType,
    title: string,
    message: string,
    email: boolean = false,
    inApp: boolean = true,
    push: boolean = false,
    additionalData?: Partial<NotificationPayload>
  ) => {
    return notificationManager.notifyWithChannels(
      type,
      title,
      message,
      email,
      inApp,
      push,
      additionalData
    );
  };

  return {
    setUserPreferences,
    sendNotification,
    sendSimpleNotification,
    manager: notificationManager,
  };
}

// Export the manager for advanced usage
export { NotificationManager };
