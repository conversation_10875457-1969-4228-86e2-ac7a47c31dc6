"use client";

/**
 * RB<PERSON> Hooks
 *
 * Re-exports validation hooks from the main hooks location for backward compatibility.
 * Management hooks have been removed - only validation functions remain.
 */

// Re-export validation hooks from the main location
export {
  useRBAC,
  usePermissions,
  useMultiplePermissions,
  useEntityPermissions,
  useUserProfile,
} from "@/hooks/useRBAC";

// Keep backward compatibility
export {
  usePermissions as useRBACPermissions,
  useUserProfile as useProfile,
} from "@/hooks/useRBAC";
