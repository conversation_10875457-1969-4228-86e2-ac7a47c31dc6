import { z } from "zod";
import { BaseService, DatabaseOptions, ServiceResponse } from "./base";
import { prisma } from "@/lib/common/prisma";
import {
  CreateProposalSchema,
  UpdateProposalSchema,
  ProposalQuerySchema,
} from "@/lib/api/validators/schemas";
import { DatabaseCrypto } from "@/lib/crypto/middleware";
import { PaginationOptions } from "@/lib/pagination";
import { DocumentService } from "./document";
import { notificationService } from "./notification";
import {
  sendNotificationToContext,
  sendNotificationToUser,
  type NotificationSocketData,
} from "@/lib/socket/server";

/**
 * Proposal service configuration
 */
export interface ProposalServiceConfig {
  enableLogging?: boolean;
  throwOnError?: boolean;
  validateInput?: boolean;
  validateOutput?: boolean;
  requireAuth?: boolean;
}

/**
 * Proposal service for handling proposal-related operations
 *
 * This service extends BaseService and provides:
 * 1. CRUD operations for proposals
 * 2. Input/output validation using Zod schemas
 * 3. Authentication and authorization checks
 * 4. Standardized error handling and logging
 */
export class ProposalService extends BaseService {
  private authRequired: boolean;

  constructor(config: ProposalServiceConfig = {}) {
    super(config);
    this.authRequired = config.requireAuth ?? true;
    // Set the model to the proposal model delegate
    this.setModel(prisma.proposal as any);
  }

  /**
   * Search proposals with single query parameter
   * @param searchParams - Object containing search query and pagination
   */
  async searchProposals(searchParams: {
    query?: string;
    page?: number;
    limit?: number;
  }): Promise<any> {
    return this.executeOperation(
      async () => {
        // Require authentication if enabled
        if (this.authRequired) {
          this.requireAuth();
        }

        // Get current user's account
        const account = await this.getCurrentUserAccount();

        // Build dynamic where conditions for proposals
        const whereConditions: any = {
          accountId: account.id, // Proposals are directly linked to accounts
        };

        // Add search filters using OR logic for partial string matching
        if (searchParams.query && searchParams.query.trim()) {
          const searchQuery = searchParams.query.trim();

          // Create OR conditions for searching across multiple fields in proposals
          const searchConditions = [
            // Search in proposal name
            {
              name: {
                contains: searchQuery,
                mode: "insensitive",
              },
            },
            // Search in proposal description
            {
              description: {
                contains: searchQuery,
                mode: "insensitive",
              },
            },
            // Search in client name through account relationship
            {
              account: {
                user: {
                  name: {
                    contains: searchQuery,
                    mode: "insensitive",
                  },
                },
              },
            },
            // Search in client email through account relationship
            {
              account: {
                user: {
                  email: {
                    contains: searchQuery,
                    mode: "insensitive",
                  },
                },
              },
            },
          ];

          // Add search conditions to where clause for proposals
          whereConditions.AND = [
            { accountId: account.id }, // Maintain access control
            { OR: searchConditions }, // Add search conditions
          ];
        }

        // Set pagination parameters
        const page = searchParams.page || 1;
        const limit = searchParams.limit || 10;
        const skip = (page - 1) * limit;

        // Build query options for proposals
        const options: DatabaseOptions = {
          where: whereConditions,
          select: {
            id: true,
            name: true,
            description: true,
            status: true,
            links: true,
            milestones: true,
            fixed_budget: true,
            total_budget: true,
            duration: true,
            agreed_to_terms_and_conditions: true,
            createdAt: true,
            updatedAt: true,
            account: {
              select: {
                id: true,
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                  },
                },
              },
            },
          },
          skip,
          take: limit,
          orderBy: {
            createdAt: "desc",
          },
        };

        // Get proposals and total count
        const [proposals, totalCount] = await Promise.all([
          this.findManyRecords(options),
          this.countRecords({ where: whereConditions }),
        ]);

        // Transform proposals to match API schema (same as getProposals)
        const transformedProposals = proposals.map((proposal: any) => ({
          id: proposal.id,
          name: proposal.name,
          description: proposal.description,
          status: proposal.status,
          links: proposal.links,
          milestones: proposal.milestones,
          fixed_budget: proposal.fixed_budget,
          total_budget: proposal.total_budget,
          duration: proposal.duration,
          agreed_to_terms_and_conditions:
            proposal.agreed_to_terms_and_conditions,
          client: {
            name: proposal.account.user.name,
            email: proposal.account.user.email,
          },
          createdAt: proposal.createdAt,
          updatedAt: proposal.updatedAt,
        }));

        // Calculate pagination metadata
        const totalPages = Math.ceil(totalCount / limit);
        const hasNextPage = page < totalPages;
        const hasPreviousPage = page > 1;

        return {
          proposals: transformedProposals,
          pagination: {
            page,
            limit,
            total: totalCount,
            totalPages,
            hasNextPage,
            hasPreviousPage,
          },
          searchQuery: searchParams.query, // Return search query for reference
        };
      },
      "searchProposals",
      true // Enable pagination in response
    );
  }

  /**
   * Create a new proposal
   * @param data - Proposal creation data
   * @returns Service response with created proposal
   */
  async createProposal(data: unknown): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        super.requireAuth();
      }

      // Handle attachment separation (same approach as contract service)
      let attachments: Record<string, any>[] = [];
      let attachment: any = undefined;
      let proposalFields: Record<string, any> = {};

      Object.entries(data as Record<string, any>).forEach(
        ([key, value]: [string, any], index: number) => {
          if (key.startsWith("attachment-")) {
            let count: number = index + 1;
            attachments.push({ ["file-" + count]: value });
          } else if (key === "attachment") attachment = value;
          else proposalFields[key] = value;
        }
      );

      this.log("info", "Creating new proposal", { name: proposalFields.name });

      // Validate input data
      const validatedData: any = this.validateInput(
        CreateProposalSchema,
        proposalFields
      );

      // Get current user's account
      const account = await this.getCurrentUserAccount();

      // Store original model for later restoration
      const originalModel: any = this.model;

      // Encrypt sensitive fields before saving
      const proposalData = {
        name: validatedData.name.trim(),
        description: validatedData.description?.trim() || null,
        status: validatedData.status || "created",
        links: validatedData.links || [],
        milestones: validatedData.milestones || [],
        fixed_budget: validatedData.fixed_budget,
        total_budget: validatedData.total_budget,
        duration: validatedData.duration,
        agreed_to_terms_and_conditions:
          validatedData.agreed_to_terms_and_conditions,
        accountId: account.id,
      };

      // Encrypt sensitive fields (description contains sensitive project details)
      const encryptedData = await DatabaseCrypto.encryptFields(proposalData, [
        "description",
      ]);

      // Prepare create options with contextual structure
      const options: DatabaseOptions = {
        data: encryptedData,
        select: {
          id: true,
          name: true,
          description: true,
          status: true,
          links: true,
          milestones: true,
          fixed_budget: true,
          total_budget: true,
          duration: true,
          agreed_to_terms_and_conditions: true,
          createdAt: true,
          updatedAt: true,
          accountId: true,
        },
      };

      // Create the proposal using base CRUD operation
      const newProposal: any = await this.createRecord(options);

      // Handle attachments after proposal creation (same approach as contract service)
      const associations: any = {
        category: "proposals",
        association_entity: "proposal",
        association_id: newProposal.id,
      };

      // Handle single attachment update (existing document)
      if (attachment) {
        this.setModel(prisma.document as any);
        await this.updateRecord({
          where: { id: attachment?.id },
          data: associations,
        }).finally(() => {
          this.setModel(originalModel);
        });
      }

      // Handle multiple new attachments
      if (attachments && attachments?.length > 0) {
        // Set model to attachment for creating attachments
        let documentsWithAssociations = attachments.map((file: any) => ({
          ...file,
          ...associations,
        }));

        let document = new DocumentService({
          requireAuth: false,
        });

        document.bulkCreateDocuments(documentsWithAssociations);
      }

      this.log("info", `Proposal created successfully: ${newProposal.id}`);

      // Send notifications about proposal creation
      await this.createProposalNotifications(newProposal.id, newProposal.name, {
        id: account.userId,
        name: account.user?.name,
      });

      return newProposal;
    }, "createProposal");
  }

  /**
   * Update an existing proposal
   * @param data - Proposal update data
   * @returns Service response with updated proposal
   */
  async updateProposal(data: unknown): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        super.requireAuth();
      }

      // Handle attachment separation (same approach as contract service)
      let attachments: Record<string, any>[] = [];
      let attachment: any = undefined;
      let proposalUpdateFields: Record<string, any> = {};

      Object.entries(data as Record<string, any>).forEach(
        ([key, value]: [string, any], index: number) => {
          if (key.startsWith("attachment-")) {
            let count: number = index + 1;
            attachments.push({ ["file-" + count]: value });
          } else if (key === "attachment") attachment = value;
          else proposalUpdateFields[key] = value;
        }
      );

      this.log("info", "Updating proposal", { id: proposalUpdateFields.id });

      // Validate input data
      const validatedData: any = this.validateInput(
        UpdateProposalSchema,
        proposalUpdateFields
      );

      // Store original model for later restoration
      const originalModel: any = this.model;

      // Build update data (only include provided fields)
      const updateData: any = {};
      Object.entries(validatedData).forEach(([key, value]: [string, any]) => {
        if (value !== undefined) updateData[key] = value;
      });

      // Encrypt sensitive fields (description contains sensitive project details)
      const encryptedData = await DatabaseCrypto.encryptFields(updateData, [
        "description",
      ]);

      // Prepare update options with contextual structure
      const options: DatabaseOptions = {
        where: { id: encryptedData.id },
        data: encryptedData,
        select: {
          id: true,
          name: true,
          description: true,
          status: true,
          links: true,
          milestones: true,
          fixed_budget: true,
          total_budget: true,
          duration: true,
          agreed_to_terms_and_conditions: true,
          createdAt: true,
          updatedAt: true,
          accountId: true,
          account: {
            select: {
              id: true,
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
        },
      };

      // Update the proposal using base CRUD operation
      // Prisma will automatically throw "Record to update not found" if proposal doesn't exist or user lacks access
      const updatedProposal: any = await this.updateRecord(options);

      // Handle attachments after proposal update (same approach as contract service)
      const associations: any = {
        category: "proposals",
        association_entity: "proposal",
        association_id: updatedProposal.id,
      };

      // Handle single attachment update (existing document)
      if (attachment) {
        this.setModel(prisma.document as any);
        await this.updateRecord({
          where: { id: attachment.id },
          data: associations,
        }).finally(() => {
          this.setModel(originalModel);
        });
      }

      // Handle multiple new attachments
      if (attachments && attachments?.length > 0) {
        // Set model to attachment for creating attachments
        let documentsWithAssociations = attachments.map((file: any) => ({
          ...file,
          ...associations,
        }));

        let document = new DocumentService({
          requireAuth: false,
        });

        document.bulkCreateDocuments(documentsWithAssociations);
      }

      this.log("info", `Proposal updated successfully: ${updatedProposal.id}`);

      // Send notifications about proposal update
      const changes = Object.keys(validatedData).filter(
        (key) => validatedData[key] !== undefined
      );
      await this.createProposalUpdateNotifications(
        updatedProposal.id,
        updatedProposal.name,
        { id: this.getCurrentUserId(), name: this.context?.user?.name },
        changes
      );

      return updatedProposal;
    }, "updateProposal");
  }

  /**
   * Get proposals with filtering and pagination
   * @param query - Query parameters
   * @param paginationOptions - Pagination parameters (page, limit, cursor, orderBy)
   * @returns Service response with proposals
   */
  async getProposals(
    query: unknown,
    paginationOptions: PaginationOptions = {}
  ): Promise<ServiceResponse> {
    return this.executeOperation(
      async () => {
        // Require authentication if enabled
        if (this.authRequired) {
          super.requireAuth();
        }

        // Set pagination parameters
        this.handlePagination(paginationOptions);

        // Validate query parameters
        const validatedQuery: any = this.validateInput(
          ProposalQuerySchema,
          query || {}
        );

        // Build where clause
        let where: any = {};
        if (validatedQuery.id) where.id = validatedQuery.id;
        if (validatedQuery.status) where.status = validatedQuery.status;
        if (validatedQuery.accountId)
          where.accountId = validatedQuery.accountId;

        // Add user access filter if authenticated
        const currentUser = await this.getCurrentUser();
        const currentUserAccount = await this.getCurrentUserAccount();

        if (currentUser?.role?.name !== "admin" && currentUserAccount) {
          where.accountId = currentUserAccount.id;
        }

        // Build base query options
        const baseOptions: DatabaseOptions = {
          where,
          select: {
            id: true,
            name: true,
            description: true,
            status: true,
            links: true,
            milestones: true,
            fixed_budget: true,
            total_budget: true,
            duration: true,
            agreed_to_terms_and_conditions: true,
            createdAt: true,
            updatedAt: true,
            account: {
              select: {
                id: true,
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                  },
                },
              },
            },
          },
        };

        // Handle single proposal request
        if (validatedQuery.id) {
          const options = { ...baseOptions, where: { id: validatedQuery.id } };
          const proposal: any = await this.findUniqueRecord(options);

          if (!proposal) {
            throw new Error("Proposal not found");
          }

          // Store original model for later restoration
          const originalModel: any = this.model;

          // Retrieve attachments for single proposal (same approach as contract service)
          this.setModel(prisma.document as any);
          let attachments: any[] = await this.findManyRecords({
            where: {
              association_id: proposal.id,
              association_entity: "proposal",
            },
            select: {
              id: true,
              name: true,
              path: true,
              file_type: true,
              size: true,
            },
          });

          // Transform proposal with attachments
          const transformedProposal = {
            ...proposal,
            client: {
              name: proposal.account.user.name,
              email: proposal.account.user.email,
            },
            attachments: attachments,
          };

          // Reset Model Back to Original
          this.setModel(originalModel);

          return transformedProposal;
        }

        // Apply pagination query options for multiple proposals
        const paginationQuery = this.getPrismaOffsetQuery();
        const options: DatabaseOptions = {
          ...baseOptions,
          ...paginationQuery,
        };

        // Handle multiple proposals request
        const proposals = await this.findManyRecords(options);

        let transformedProposals: any[] = proposals?.map((proposal: any) => ({
          ...proposal,
          client: {
            name: proposal.account.user.name,
            email: proposal.account.user.email,
          },
        }));

        return transformedProposals;
      },
      "getProposals",
      true
    ); // Enable pagination in response
  }

  /**
   * Delete a proposal
   * @param proposalId - ID of the proposal to delete
   * @returns Service response
   */
  async deleteProposal(proposalId: string): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        super.requireAuth();
      }

      // Validate proposal ID
      const validatedId = this.validateInput(z.string().min(1), proposalId);

      // Build where clause with user access control
      const currentUserId = this.getCurrentUserId();
      const where = {
        id: validatedId,
        ...(currentUserId && {
          account: {
            userId: currentUserId,
          },
        }),
      };

      // Get proposal data before deletion for notifications
      const proposalToDelete: any = await this.findUniqueRecord({
        where,
        select: {
          id: true,
          name: true,
        },
      });

      if (!proposalToDelete) {
        throw new Error("Proposal not found or access denied");
      }

      // Prepare delete options with contextual structure
      const options: DatabaseOptions = {
        where,
      };

      // Delete the proposal using base CRUD operation
      // Prisma will automatically throw "Record to delete does not exist" if proposal doesn't exist or user lacks access
      await this.deleteRecord(options);

      // Send notifications about proposal deletion
      await this.createProposalDeleteNotifications(
        proposalToDelete.id,
        proposalToDelete.name,
        { id: currentUserId, name: this.context?.user?.name }
      );

      return { message: "Proposal deleted successfully" };
    }, "deleteProposal");
  }

  /**
   * Create notifications for proposal creation
   */
  private async createProposalNotifications(
    proposalId: string,
    proposalName: string,
    creator: any
  ): Promise<void> {
    try {
      const notificationData = {
        title: `New proposal created: ${proposalName}`,
        message: `${creator.name || "Someone"} created a new proposal`,
        type: "proposal" as const,
        category: "inApp" as const,
        userId: creator.id,
        data: {
          proposalId: proposalId,
          proposalName: proposalName,
          creatorId: creator.id,
          creatorName: creator.name,
          actionUrl: `/proposals/${proposalId}`,
        },
        priority: "normal" as const,
        sourceId: proposalId,
        sourceType: "proposal",
      };

      // Create notification in database
      const createdNotification = await notificationService.createNotification(
        notificationData
      );

      if (createdNotification.success && createdNotification.data) {
        // Send notification via Pusher to the creator
        const pusherNotification: NotificationSocketData = {
          id: createdNotification.data.id,
          title: notificationData.title,
          message: notificationData.message,
          type: notificationData.type,
          category: notificationData.category,
          userId: notificationData.userId,
          data: notificationData.data,
          priority: notificationData.priority,
          createdAt:
            createdNotification.data.createdAt || new Date().toISOString(),
        };

        // Send to specific user (creator)
        sendNotificationToUser(creator.id, pusherNotification);
      }

      this.log(
        "info",
        `Created proposal creation notification for: ${proposalName}`
      );
    } catch (error) {
      this.log("error", `Failed to create proposal notifications: ${error}`);
      // Don't throw error to avoid breaking the main proposal creation flow
    }
  }

  /**
   * Create notifications for proposal updates
   */
  private async createProposalUpdateNotifications(
    proposalId: string,
    proposalName: string,
    updater: any,
    changes: string[]
  ): Promise<void> {
    try {
      const changesText = changes.length > 0 ? ` (${changes.join(", ")})` : "";

      const notificationData = {
        title: `Proposal updated: ${proposalName}`,
        message: `${
          updater.name || "Someone"
        } updated the proposal${changesText}`,
        type: "proposal" as const,
        category: "inApp" as const,
        userId: updater.id,
        data: {
          proposalId: proposalId,
          proposalName: proposalName,
          updaterId: updater.id,
          updaterName: updater.name,
          changes: changes,
          actionUrl: `/proposals/${proposalId}`,
        },
        priority: "normal" as const,
        sourceId: proposalId,
        sourceType: "proposal",
      };

      // Create notification in database
      const createdNotification = await notificationService.createNotification(
        notificationData
      );

      if (createdNotification.success && createdNotification.data) {
        // Send notification via Pusher to the updater
        const pusherNotification: NotificationSocketData = {
          id: createdNotification.data.id,
          title: notificationData.title,
          message: notificationData.message,
          type: notificationData.type,
          category: notificationData.category,
          userId: notificationData.userId,
          data: notificationData.data,
          priority: notificationData.priority,
          createdAt:
            createdNotification.data.createdAt || new Date().toISOString(),
        };

        // Send to specific user (updater)
        sendNotificationToUser(updater.id, pusherNotification);
      }

      this.log(
        "info",
        `Created proposal update notification for: ${proposalName}`
      );
    } catch (error) {
      this.log(
        "error",
        `Failed to create proposal update notifications: ${error}`
      );
      // Don't throw error to avoid breaking the main proposal update flow
    }
  }

  /**
   * Create notifications for proposal deletion
   */
  private async createProposalDeleteNotifications(
    proposalId: string,
    proposalName: string,
    deleter: any
  ): Promise<void> {
    try {
      const notificationData = {
        title: `Proposal deleted: ${proposalName}`,
        message: `${deleter.name || "Someone"} deleted the proposal`,
        type: "proposal" as const,
        category: "inApp" as const,
        userId: deleter.id,
        data: {
          proposalId: proposalId,
          proposalName: proposalName,
          deleterId: deleter.id,
          deleterName: deleter.name,
          actionUrl: `/proposals`,
        },
        priority: "high" as const,
        sourceId: proposalId,
        sourceType: "proposal",
      };

      // Create notification in database
      const createdNotification = await notificationService.createNotification(
        notificationData
      );

      if (createdNotification.success && createdNotification.data) {
        // Send notification via Pusher to the deleter
        const pusherNotification: NotificationSocketData = {
          id: createdNotification.data.id,
          title: notificationData.title,
          message: notificationData.message,
          type: notificationData.type,
          category: notificationData.category,
          userId: notificationData.userId,
          data: notificationData.data,
          priority: notificationData.priority,
          createdAt:
            createdNotification.data.createdAt || new Date().toISOString(),
        };

        // Send to specific user (deleter)
        sendNotificationToUser(deleter.id, pusherNotification);
      }

      this.log(
        "info",
        `Created proposal deletion notification for: ${proposalName}`
      );
    } catch (error) {
      this.log(
        "error",
        `Failed to create proposal deletion notifications: ${error}`
      );
      // Don't throw error to avoid breaking the main proposal deletion flow
    }
  }
}
