import { BaseService } from "./base";
import { prisma } from "@/lib/common/prisma";

import {
  CreateRoomSchema,
  CreateMessageSchema,
  type CreateRoom,
  type CreateMessage,
  type UserState,
} from "../validators/schemas/chat";

import { DocumentService } from "./document";
import { notificationService } from "./notification";
import {
  sendNotificationToContext,
  sendNotificationToUser,
  broadcastUserStateToContext,
  broadcastUserStateToUser,
  type NotificationSocketData,
} from "@/lib/socket/server";

// Chat statistics type
interface ChatStatistics {
  totalRooms: number;
  totalMessages: number;
  activeUsers: number;
  onlineUsers: number;
}

export class ChatService extends BaseService {
  private requiredAuth: boolean;

  // In-memory state for real-time features (typing indicators, user states)
  private userStates: Map<string, UserState> = new Map(); // accountId -> UserState
  private typingUsers: Map<string, Set<string>> = new Map(); // roomId -> Set of accountIds

  constructor({ requiredAuth = true }: { requiredAuth?: boolean } = {}) {
    super();

    this.requiredAuth = requiredAuth;
    this.setModel(prisma.room as any);
  }

  /**
   * Get all rooms for a user
   */
  async getRooms(accountId?: string): Promise<any> {
    return this.executeOperation(async () => {
      this.log("info", "Fetching chat rooms", { accountId });

      // Get current user's account if accountId not provided
      const { id: targetAccountId } = await this.getCurrentUserAccount();
      const finalAccountId = accountId || targetAccountId;

      if (!finalAccountId) {
        throw new Error("Account ID is required");
      }

      // Query rooms where user is a member
      const rooms = await this.findManyRecords({
        where: {
          OR: [
            { accountId: finalAccountId },
            { members: { some: { accountId: finalAccountId } } },
          ],
        },
        include: {
          members: true,
          messages: {
            orderBy: {
              createdAt: "desc",
            },
            take: 1,
            include: {
              attachments: true,
            },
          },
          contract: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          updatedAt: "desc",
        },
      });

      // Transform to match expected format
      const enrichedRooms = rooms.map((room: any) => ({
        ...room,
        lastMessage: room.messages[0] || null,
        unreadCount: 0, // TODO: Implement unread count logic
      }));

      this.log("info", `Found ${enrichedRooms.length} rooms`);

      return enrichedRooms;
    }, "getRooms");
  }

  async enrichMessageWithSender(messages: any[]) {
    this.setModel(prisma.account as any);

    return messages.map(async (message: any) => {
      const account = await this.findUniqueRecord({
        where: { id: message.sent_from },
        select: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
            },
          },
        },
      });

      return {
        ...message,
        sender: (account as any)?.user || null,
      };
    });
  }

  async enrichMessageWithAttachments(messages: any[]) {
    this.setModel(prisma.document as any);

    return messages.map(async (message: any) => {
      const attachments = await this.findManyRecords({
        where: { association_id: message.id, association_entity: "message" },
        select: {
          id: true,
          name: true,
          file_type: true,
          path: true,
        },
      });

      let remapedAttachments = attachments.map((attachment: any) => ({
        content: attachment,
        entity: "attachment",
        id: attachment.id,
      }));

      return {
        ...message,
        associations: [...message.associations, ...remapedAttachments],
      };
    });
  }

  /**
   * Get messages for a room
   */
  async getMessages(roomId: string, limit = 50, offset = 0): Promise<any> {
    return this.executeOperation(async () => {
      this.log("info", `Fetching messages for room: ${roomId}`, {
        limit,
        offset,
      });

      const originalModel = this.model || (prisma.room as any);

      try {
        // Verify user has access to this room
        const currentAccount = await this.getCurrentUserAccount();

        // Set model to member for access check
        this.setModel(prisma.member as any);
        const roomAccess = await this.findFirstRecord({
          where: {
            roomId: roomId,
            accountId: currentAccount.id,
          },
        });

        if (!roomAccess) {
          throw new Error("Access denied to this room");
        }

        // Set model to message for querying messages
        this.setModel(prisma.message as any);
        const messages = await this.findManyRecords({
          where: { roomId: roomId },
          orderBy: { createdAt: "asc" },
        });

        // Set model to account for enriching with sender info
        let enrichedSenders = await Promise.all(
          await this.enrichMessageWithSender(messages)
        );
        let enrichedAttachments = await Promise.all(
          await this.enrichMessageWithAttachments(enrichedSenders)
        );

        let enrichedMessages = enrichedAttachments;

        this.log("info", `Found ${enrichedMessages.length} room messages`);

        return enrichedMessages;
      } finally {
        // Restore original model
        this.setModel(originalModel);
      }
    }, "getMessages");
  }

  /**
   * Send a message
   */
  async sendMessage(messageData: CreateMessage): Promise<any> {
    return this.executeOperation(async () => {
      this.log("info", "Sending message", { roomId: messageData.roomId });

      let attachments: Record<string, any>[] = [];
      let messages: Record<string, any> = {};

      Object.entries(messageData).forEach(
        ([key, value]: [string, any], index: number) => {
          if (key.startsWith("attachment-")) {
            let count: number = index + 1;
            attachments.push({ ["file-" + count]: value });
          } else messages[key] = value;
        }
      );

      // Validate input
      const validatedData = CreateMessageSchema.parse(messages);

      // Ensure associations is an array
      const messageToCreate = {
        ...validatedData,
        associations: validatedData.associations || [],
      };

      const originalModel = this.model || (prisma.room as any);

      try {
        // Get current user account
        const currentAccount = await this.getCurrentUserAccount();

        // Verify user has access to the room
        if (messageToCreate.roomId) {
          // Set model to member for access check
          this.setModel(prisma.member as any);
          const roomAccess = await this.findFirstRecord({
            where: {
              roomId: messageToCreate.roomId,
              accountId: currentAccount.id,
            },
          });

          if (!roomAccess) {
            throw new Error("Access denied to this room");
          }
        }

        // Set model to message for creating message
        this.setModel(prisma.message as any);
        const newMessage: { id: string } = await this.createRecord({
          data: messageToCreate,
          select: { id: true },
        });

        if (attachments && attachments?.length > 0) {
          // Set model to attachment for creating attachments
          let documentsWithAssociations = attachments.map((file: any) => ({
            ...file,
            category: "messages",
            association_entity: "message",
            association_id: newMessage?.id || "",
          }));

          let document = new DocumentService({
            requireAuth: false,
          });

          document.bulkCreateDocuments(documentsWithAssociations);
        }

        // Update room's updatedAt timestamp
        if (messageToCreate.roomId) {
          // Set model back to room for updating
          this.setModel(prisma.room as any);
          await this.updateRecord({
            where: { id: messageToCreate.roomId },
            data: { updatedAt: new Date() },
          });
        }

        this.log("info", `Message sent: ${(newMessage as any).id}`);

        // Broadcast message to chat context for real-time updates
        if (messageToCreate.roomId) {
          // Broadcast new message event to all room members
          const messageData = {
            id: newMessage.id,
            content: messageToCreate.content,
            roomId: messageToCreate.roomId,
            senderId: currentAccount.id,
            senderName: currentAccount.name,
            timestamp: new Date().toISOString(),
            associations: messageToCreate.associations || [],
          };

          // Broadcast to chat context (room channel)
          const broadcastResult = this.broadcastMessageToRoom(
            messageToCreate.roomId,
            messageData,
            currentAccount.id
          );

          // Log broadcast result for debugging
          this.log("info", "Message broadcast result:", broadcastResult);

          // Create and send notifications to room members
          await this.createMessageNotifications(
            messageToCreate.roomId,
            newMessage.id,
            messageToCreate.content || "New message",
            currentAccount
          );
        }

        return newMessage;
      } finally {
        // Restore original model
        this.setModel(originalModel);
      }
    }, "sendMessage");
  }

  /**
   * Update a message
   */
  async updateMessage(messageId: string, content: string): Promise<any> {
    return this.executeOperation(async () => {
      this.log("info", "Updating message", { messageId });

      const originalModel = this.model || (prisma.message as any);

      try {
        // Get current user account
        const currentAccount = await this.getCurrentUserAccount();

        // Set model to message for finding and updating
        this.setModel(prisma.message as any);

        // First, get the existing message to verify ownership and get room info
        const existingMessage: any = await this.findUniqueRecord({
          where: { id: messageId },
          select: {
            id: true,
            sent_from: true,
            roomId: true,
            content: true,
          },
        });

        if (!existingMessage) {
          throw new Error("Message not found");
        }

        // Verify user owns the message
        if (existingMessage.sent_from !== currentAccount.id) {
          throw new Error(
            "Access denied: You can only update your own messages"
          );
        }

        // Update the message
        const updatedMessage: any = await this.updateRecord({
          where: { id: messageId },
          data: {
            content,
            updatedAt: new Date(),
          },
          select: {
            id: true,
            content: true,
            roomId: true,
            updatedAt: true,
          },
        });

        this.log("info", `Message updated: ${messageId}`);

        // Broadcast message update to chat context for real-time updates
        if (existingMessage.roomId) {
          const messageData = {
            id: updatedMessage.id,
            content: updatedMessage.content,
            roomId: existingMessage.roomId,
            senderId: currentAccount.id,
            senderName: currentAccount.name,
            timestamp: updatedMessage.updatedAt.toISOString(),
          };

          const broadcastResult = this.broadcastMessageUpdate(
            existingMessage.roomId,
            messageData,
            currentAccount.id
          );

          // Log broadcast result for debugging
          this.log("info", "Message update broadcast result:", broadcastResult);
        }

        return updatedMessage;
      } finally {
        // Restore original model
        this.setModel(originalModel);
      }
    }, "updateMessage");
  }

  /**
   * Delete a message
   */
  async deleteMessage(messageId: string): Promise<any> {
    return this.executeOperation(async () => {
      this.log("info", "Deleting message", { messageId });

      const originalModel = this.model || (prisma.message as any);

      try {
        // Get current user account
        const currentAccount = await this.getCurrentUserAccount();

        // Set model to message for finding and deleting
        this.setModel(prisma.message as any);

        // First, get the existing message to verify ownership and get room info
        const existingMessage: any = await this.findUniqueRecord({
          where: { id: messageId },
          select: {
            id: true,
            sent_from: true,
            roomId: true,
            content: true,
          },
        });

        if (!existingMessage) {
          throw new Error("Message not found");
        }

        // Verify user owns the message
        if (existingMessage.sent_from !== currentAccount.id) {
          throw new Error(
            "Access denied: You can only delete your own messages"
          );
        }

        // Delete the message
        await this.deleteRecord({
          where: { id: messageId },
        });

        this.log("info", `Message deleted: ${messageId}`);

        // Broadcast message deletion to chat context for real-time updates
        if (existingMessage.roomId) {
          const messageData = {
            id: messageId,
            roomId: existingMessage.roomId,
            senderId: currentAccount.id,
            senderName: currentAccount.name,
            timestamp: new Date().toISOString(),
          };

          const broadcastResult = this.broadcastMessageDeletion(
            existingMessage.roomId,
            messageData,
            currentAccount.id
          );

          // Log broadcast result for debugging
          this.log(
            "info",
            "Message deletion broadcast result:",
            broadcastResult
          );
        }

        return { success: true, messageId };
      } finally {
        // Restore original model
        this.setModel(originalModel);
      }
    }, "deleteMessage");
  }

  /**
   * Create a new room
   */
  async createRoom(roomData: CreateRoom): Promise<any> {
    try {
      // Validate input
      const validatedData: any = CreateRoomSchema.parse(roomData);

      this.log("info", "Creating new room", { name: validatedData.name });

      const currentAccount: any = await this.getCurrentUserAccount();

      if (!currentAccount) {
        throw new Error("Unauthorized");
      }

      validatedData.accountId = currentAccount.id;

      // Add members to room
      let room: any = await this.createRecord({
        data: validatedData,
        select: {
          id: true,
          name: true,
          about: true,
          contractId: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      this.log("info", `Room created: ${room.name}`);

      // Broadcast room creation to relevant contexts
      if (room.contractId) {
        this.broadcastRoomCreation(room, currentAccount);
      }

      // Create notifications for room creation
      await this.createRoomNotifications(
        room.id,
        room.name || "New Chat Room",
        currentAccount,
        room.contractId
      );

      return this.createSuccessResponse(room, 201, "Room created successfully");
    } catch (error) {
      this.log("error", `Error creating room: ${error}`);
      return this.createErrorResponse("Failed to create room", 500);
    }
  }

  /**
   * Update user state (online/offline/typing/away)
   */
  async updateUserState(
    accountId: string,
    state: UserState,
    roomId?: string
  ): Promise<any> {
    return this.executeOperation(async () => {
      this.log("info", `Updating user state: ${accountId} -> ${state}`, {
        roomId,
      });

      // Update in-memory user state for real-time features
      this.userStates.set(accountId, state);

      // Handle typing state in memory (for real-time typing indicators)
      if (state === "typing" && roomId) {
        if (!this.typingUsers.has(roomId)) {
          this.typingUsers.set(roomId, new Set());
        }
        this.typingUsers.get(roomId)!.add(accountId);
      } else if (roomId && this.typingUsers.has(roomId)) {
        this.typingUsers.get(roomId)!.delete(accountId);
      }

      // Get user info for broadcasting
      const originalModel = this.model;
      try {
        this.setModel(prisma.account as any);
        const account = await this.findUniqueRecord({
          where: { id: accountId },
          include: {
            user: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        });

        const userName = (account as any)?.user?.name;
        const userId = (account as any)?.user?.id;

        // Broadcast user state changes to room members via Pusher
        if (roomId && userId) {
          broadcastUserStateToContext("chat", roomId, {
            userId,
            userName,
            state,
            timestamp: new Date().toISOString(),
          });
        } else if (userId) {
          // Broadcast globally to user's contacts/connections
          broadcastUserStateToUser(userId, {
            userId,
            userName,
            state,
            timestamp: new Date().toISOString(),
          });
        }

        this.log("info", `User state updated and broadcasted: ${accountId}`);

        return { userId: accountId, state, roomId }; // Return userId for backward compatibility
      } finally {
        if (originalModel) {
          this.setModel(originalModel);
        }
      }
    }, "updateUserState");
  }

  /**
   * Get typing users for a room
   */
  async getTypingUsers(roomId: string): Promise<any> {
    return this.executeOperation(async () => {
      const typingAccountIds = Array.from(this.typingUsers.get(roomId) || []);

      if (typingAccountIds.length === 0) {
        return [];
      }

      const originalModel = this.model || (prisma.room as any);

      try {
        // Set model to account for querying user info
        this.setModel(prisma.account as any);
        const typingUsers = await this.findManyRecords({
          where: {
            id: {
              in: typingAccountIds, // These are accountIds
            },
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              },
            },
          },
        });

        return (typingUsers as any[])
          .map((account) => ({
            id: account.id, // accountId
            name: account.user?.name,
            email: account.user?.email,
            avatar: account.user?.image,
          }))
          .filter(Boolean);
      } finally {
        // Restore original model
        this.setModel(originalModel);
      }
    }, "getTypingUsers");
  }

  /**
   * Broadcast message to room members via Pusher
   */
  private broadcastMessageToRoom(
    roomId: string,
    messageData: any,
    senderId: string
  ): { success: boolean; eventData?: any } {
    try {
      const eventData = {
        id: `msg-${messageData.id}`,
        title: "New Message",
        message: messageData.content,
        type: "chat_message", // Specific type for message events
        category: "realtime",
        userId: senderId,
        data: {
          ...messageData,
          eventType: "message_created", // Specific event type for mutations
          mutationTrigger: true, // Flag to indicate this should trigger mutations
        },
        priority: "normal",
        createdAt: messageData.timestamp,
      };

      // Broadcast message event specifically for chat mutations
      sendNotificationToContext("chat", roomId, eventData);

      this.log("info", `Message broadcasted to room: ${roomId}`, {
        messageId: messageData.id,
        eventType: "message_created",
      });

      return { success: true, eventData };
    } catch (error) {
      this.log("error", `Failed to broadcast message to room: ${error}`);
      // Don't throw error to avoid breaking the main message flow
      return { success: false };
    }
  }

  /**
   * Broadcast message update to room members via Pusher
   */
  private broadcastMessageUpdate(
    roomId: string,
    messageData: any,
    senderId: string
  ): { success: boolean; eventData?: any } {
    try {
      const eventData = {
        id: `msg-update-${messageData.id}`,
        title: "Message Updated",
        message: messageData.content,
        type: "chat_message", // Specific type for message events
        category: "realtime",
        userId: senderId,
        data: {
          ...messageData,
          eventType: "message_updated", // Specific event type for mutations
          mutationTrigger: true, // Flag to indicate this should trigger mutations
        },
        priority: "normal",
        createdAt: messageData.timestamp,
      };

      // Broadcast message update event specifically for chat mutations
      sendNotificationToContext("chat", roomId, eventData);

      this.log("info", `Message update broadcasted to room: ${roomId}`, {
        messageId: messageData.id,
        eventType: "message_updated",
      });

      return { success: true, eventData };
    } catch (error) {
      this.log("error", `Failed to broadcast message update to room: ${error}`);
      // Don't throw error to avoid breaking the main message flow
      return { success: false };
    }
  }

  /**
   * Broadcast message deletion to room members via Pusher
   */
  private broadcastMessageDeletion(
    roomId: string,
    messageData: any,
    senderId: string
  ): { success: boolean; eventData?: any } {
    try {
      const eventData = {
        id: `msg-delete-${messageData.id}`,
        title: "Message Deleted",
        message: "A message was deleted",
        type: "chat_message", // Specific type for message events
        category: "realtime",
        userId: senderId,
        data: {
          ...messageData,
          eventType: "message_deleted", // Specific event type for mutations
          mutationTrigger: true, // Flag to indicate this should trigger mutations
        },
        priority: "normal",
        createdAt: messageData.timestamp,
      };

      // Broadcast message deletion event specifically for chat mutations
      sendNotificationToContext("chat", roomId, eventData);

      this.log("info", `Message deletion broadcasted to room: ${roomId}`, {
        messageId: messageData.id,
        eventType: "message_deleted",
      });

      return { success: true, eventData };
    } catch (error) {
      this.log(
        "error",
        `Failed to broadcast message deletion to room: ${error}`
      );
      // Don't throw error to avoid breaking the main message flow
      return { success: false };
    }
  }

  /**
   * Broadcast room creation to contract context
   */
  private broadcastRoomCreation(room: any, creator: any): void {
    try {
      if (room.contractId) {
        // Broadcast room creation to contract context
        sendNotificationToContext(
          "contract",
          room.contractId,
          {
            id: `room-${room.id}`,
            title: "New Chat Room Created",
            message: `${creator.name || "Someone"} created a new chat room: ${
              room.name
            }`,
            type: "room_created",
            category: "realtime",
            userId: creator.id,
            data: {
              roomId: room.id,
              roomName: room.name,
              creatorId: creator.id,
              creatorName: creator.name,
              contractId: room.contractId,
              actionUrl: `/chat/${room.id}`,
            },
            priority: "normal",
            createdAt: new Date().toISOString(),
          },
          creator.id
        ); // Exclude creator from receiving the broadcast

        this.log(
          "info",
          `Room creation broadcasted to contract: ${room.contractId}`
        );
      }
    } catch (error) {
      this.log("error", `Failed to broadcast room creation: ${error}`);
      // Don't throw error to avoid breaking the main room creation flow
    }
  }

  /**
   * Get chat statistics
   */
  async getStatistics(): Promise<any> {
    return this.executeOperation(async () => {
      this.log("info", "Fetching chat statistics");

      const originalModel = this.model || (prisma.room as any);

      try {
        // Get room count
        this.setModel(prisma.room as any);
        const totalRooms = await this.countRecords({});

        // Get message count
        this.setModel(prisma.message as any);
        const totalMessages = await this.countRecords({});

        // Count active users from in-memory state
        const activeUsers = Array.from(this.userStates.values()).filter(
          (state) => state !== "offline"
        ).length;

        const onlineUsers = Array.from(this.userStates.values()).filter(
          (state) => state === "online"
        ).length;

        const stats: ChatStatistics = {
          totalRooms,
          totalMessages,
          activeUsers,
          onlineUsers,
        };

        return stats;
      } finally {
        // Restore original model
        this.setModel(originalModel);
      }
    }, "getStatistics");
  }

  /**
   * Create notifications for new message in a room
   */
  private async createMessageNotifications(
    roomId: string,
    messageId: string,
    messageContent: string,
    sender: any
  ): Promise<void> {
    try {
      // Get all room members except the sender
      const originalModel = this.model;
      this.setModel(prisma.member as any);

      const roomMembers = await this.findManyRecords({
        where: { roomId: roomId, accountId: { not: sender.id } },
        include: {
          account: { select: { id: true, user: { select: { id: true } } } },
        },
      });

      // Get room details
      this.setModel(prisma.room as any);
      const room: any = await this.findUniqueRecord({
        where: { id: roomId },
        select: {
          id: true,
          contract: {
            select: { id: true, proposal: { select: { name: true } } },
          },
        },
      });

      if (roomMembers && roomMembers.length > 0 && room) {
        // Create notifications for each room member
        for (const member of roomMembers as any[]) {
          const roomName = room.contract.proposal.name || "Chat Room";
          const senderName = sender.name || "Someone";
          const truncatedMessage =
            messageContent.substring(0, 100) +
            (messageContent.length > 100 ? "..." : "");

          const notificationData = {
            title: `New message in ${roomName}`,
            message: `${senderName}: ${truncatedMessage}`,
            type: "chat" as const,
            category: "inApp" as const,
            userId: member.account.user.id,
            data: {
              roomId: roomId,
              messageId: messageId,
              senderId: sender.id,
              senderName: senderName,
              actionUrl: `/chat/${roomId}`,
              contractId: room.contract.id,
            },
            priority: "normal" as const,
            sourceId: messageId,
            sourceType: "message",
          };

          // Create notification in database
          const createdNotification =
            await notificationService.createNotification(notificationData);

          if (createdNotification.success && createdNotification.data) {
            // Send notification via Pusher to the specific user
            const pusherNotification: NotificationSocketData = {
              id: createdNotification.data.id,
              title: notificationData.title,
              message: notificationData.message,
              type: notificationData.type,
              category: notificationData.category,
              userId: notificationData.userId,
              data: notificationData.data,
              priority: notificationData.priority,
              createdAt:
                createdNotification.data.createdAt || new Date().toISOString(),
            };

            // Send to specific user instead of broadcasting to context to avoid sender receiving it
            sendNotificationToUser(member.account.user.id, pusherNotification);
          }
        }

        this.log(
          "info",
          `Created unified notifications for ${roomMembers.length} room members`
        );
      }

      // Restore original model
      if (originalModel) {
        this.setModel(originalModel);
      }
    } catch (error) {
      this.log("error", `Failed to create message notifications: ${error}`);
      // Don't throw error to avoid breaking the main message flow
    }
  }

  /**
   * Create notifications for new room creation
   */
  private async createRoomNotifications(
    roomId: string,
    roomName: string,
    creator: any,
    contractId?: string
  ): Promise<void> {
    try {
      // If room is associated with a contract, notify contract participants
      if (contractId) {
        const originalModel = this.model;

        // Get contract participants (assuming there's a contract participants table)
        // This is a placeholder - adjust based on your contract schema
        this.setModel(prisma.contract as any);
        const contract = await this.findUniqueRecord({
          where: { id: contractId },
          include: {
            // Adjust these relations based on your contract schema
            // participants: { include: { account: true } },
            // or however contract users are related
          },
        });

        if (contract) {
          // Create notification for contract-related room creation
          const notificationData = {
            title: `New chat room created: ${roomName}`,
            message: `${
              creator.name || "Someone"
            } created a new chat room for the contract`,
            type: "room" as const,
            category: "inApp" as const,
            userId: creator.id, // This will be overridden for each recipient
            data: {
              roomId: roomId,
              roomName: roomName,
              creatorId: creator.id,
              creatorName: creator.name,
              contractId: contractId,
              actionUrl: `/chat/${roomId}`,
            },
            priority: "normal" as const,
            sourceId: roomId,
            sourceType: "room",
          };

          // Create notification in database and send via Pusher to contract context
          const createdNotification =
            await notificationService.createNotification(notificationData);

          if (createdNotification.success && createdNotification.data) {
            // Send to contract context (excluding the creator)
            sendNotificationToContext(
              "contract",
              contractId,
              {
                id: createdNotification.data.id,
                title: notificationData.title,
                message: notificationData.message,
                type: notificationData.type,
                category: notificationData.category,
                userId: notificationData.userId,
                data: notificationData.data,
                priority: notificationData.priority,
                createdAt:
                  createdNotification.data.createdAt ||
                  new Date().toISOString(),
              },
              creator.id
            );
          }

          // Also send real-time update to contract context
          const pusherNotification: NotificationSocketData = {
            id: `room-${roomId}`,
            title: `New chat room created: ${roomName}`,
            message: `${
              creator.name || "Someone"
            } created a new chat room for the contract`,
            type: "room",
            category: "realtime",
            userId: creator.id,
            data: {
              roomId: roomId,
              roomName: roomName,
              creatorId: creator.id,
              creatorName: creator.name,
              contractId: contractId,
              actionUrl: `/chat/${roomId}`,
            },
            priority: "normal",
            createdAt: new Date().toISOString(),
          };

          sendNotificationToContext(
            "contract",
            contractId,
            pusherNotification,
            creator.id
          );
        }

        if (originalModel) {
          this.setModel(originalModel);
        }
      }

      this.log(
        "info",
        `Created room creation notification for room: ${roomName}`
      );
    } catch (error) {
      this.log("error", `Failed to create room notifications: ${error}`);
      // Don't throw error to avoid breaking the main room creation flow
    }
  }
}
