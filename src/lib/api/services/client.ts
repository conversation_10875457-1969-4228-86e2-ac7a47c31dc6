import { BaseService } from "./base";
import { prisma } from "@/lib/common/prisma";

export class ClientService extends BaseService {
  private authRequired: boolean;

  constructor(config: any = {}) {
    super(config);
    this.authRequired = config.requireAuth ?? true;
    // Set the Prisma model for database operations
    this.setModel(prisma.user as any);
  }

  /**
   * Get all clients
   */
  async getClients(): Promise<any> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      // For now, we'll return users with client role or all users as potential clients
      // This can be refined based on your specific client identification logic
      const clients: any[] = await this.findManyRecords({
        where: {
          role: {
            name: {
              contains: "client",
              mode: "insensitive",
            },
          },
        },
        select: {
          id: true,
          email: true,
          name: true,
        },
      });

      return clients;
    }, "getClients");
  }

  /**
   * Get client by ID
   */
  async getClient(id: string): Promise<any> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      if (!id) {
        throw new Error("Client ID is required");
      }

      const client: any = await this.findUniqueRecord({
        where: { id: id },
        select: {
          id: true,
          name: true,
          email: true,
        },
      });

      if (!client) {
        throw new Error("Client not found");
      }

      return client;
    }, "getClient");
  }
}
