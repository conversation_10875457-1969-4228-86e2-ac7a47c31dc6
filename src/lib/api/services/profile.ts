import { z } from "zod";
import { BaseService, DatabaseOptions, ServiceResponse } from "./base";
import { prisma } from "@/lib/common/prisma";
import {
  IdSchema,
  RequiredStringSchema,
} from "@/lib/api/validators/schemas/common";

/**
 * Profile service configuration
 */
export interface ProfileServiceConfig {
  enableLogging?: boolean;
  throwOnError?: boolean;
  validateInput?: boolean;
  validateOutput?: boolean;
  requireAuth?: boolean;
}

/**
 * Profile upsert schema (for API requests)
 */
export const UpsertProfileSchema = z.object({
  userId: IdSchema.optional(), // Optional - can be inferred from auth context
  about: z.string().optional(),
  website: z.string().url().optional().or(z.literal("")),
  location: z.string().optional(),
  socials: z.array(z.string().url()).optional(),
});

/**
 * Profile query schema
 */
export const ProfileQuerySchema = z.object({
  userId: IdSchema.optional(),
});

/**
 * Profile service for handling user profile operations
 *
 * This service extends BaseService and provides:
 * 1. Profile upsert operations (create or update)
 * 2. Profile retrieval by user ID
 * 3. Input/output validation using Zod schemas
 * 4. Authentication and authorization checks
 * 5. Standardized error handling and logging
 */
export class ProfileService extends BaseService {
  private authRequired: boolean;

  constructor(config: ProfileServiceConfig = {}) {
    super(config);
    this.authRequired = config.requireAuth ?? true;
    // Set the model to the profile model delegate
    this.setModel(prisma.profile as any);
  }

  /**
   * Upsert user profile (create if doesn't exist, update if exists)
   * @param data - Profile upsert data
   * @returns Service response with profile data
   */
  async upsertProfile(data: unknown): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        super.requireAuth();
      }

      // Validate input data
      const validatedData = this.validateInput(UpsertProfileSchema, data);

      // Get userId from validated data or auth context
      const userId = this.getCurrentUserId();

      if (!userId) {
        throw new Error("User ID is required");
      }

      console.log("\nCurrent UserID:", userId);

      // Set model back to profile
      this.setModel(prisma.profile as any);

      // Prepare profile data
      const updateProfileData = {
        about: validatedData.about?.trim() || null,
        website: validatedData.website?.trim() || null,
        location: validatedData.location?.trim() || null,
        socials: validatedData.socials || [],
      };

      const createProfileData = {
        ...updateProfileData,
        user: { connect: { id: userId } },
      };

      // Use upsert to create or update profile
      const profile = await this.upsertRecord({
        where: { userId: userId },
        update: {
          ...updateProfileData,
          updatedAt: new Date(),
        },
        create: createProfileData,
        select: {
          id: true,
          about: true,
          website: true,
          location: true,
          socials: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      return profile;
    }, "upsertProfile");
  }

  /**
   * Get user profile by user ID
   * @param userId - ID of the user whose profile to retrieve
   * @returns Service response with profile data
   */
  async getProfileByUserId(userId: string): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        super.requireAuth();
      }

      // Validate user ID
      const validatedUserId = this.validateInput(IdSchema, userId);

      // Get profile
      const profile = await this.findUniqueRecord({
        where: { userId: validatedUserId },
        select: {
          id: true,
          about: true,
          website: true,
          location: true,
          socials: true,
          userId: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      if (!profile) {
        // Return null data instead of throwing error for non-existent profiles
        return { data: null };
      }

      return profile;
    }, "getProfileByUserId");
  }

  /**
   * Delete user profile
   * @param userId - ID of the user whose profile to delete
   * @returns Service response
   */
  async deleteProfile(userId: string): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        super.requireAuth();
      }

      // Validate user ID
      const validatedUserId = this.validateInput(IdSchema, userId);

      // Check if profile exists
      const profile = await this.findUniqueRecord({
        where: { userId: validatedUserId },
      });

      if (!profile) {
        throw new Error("Profile not found");
      }

      // Delete profile
      await this.deleteRecord({
        where: { userId: validatedUserId },
      });

      return { message: "Profile deleted successfully" };
    }, "deleteProfile");
  }
}

/**
 * Type exports
 */
export type UpsertProfile = z.infer<typeof UpsertProfileSchema>;
export type ProfileQuery = z.infer<typeof ProfileQuerySchema>;
