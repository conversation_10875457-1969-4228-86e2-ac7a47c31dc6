"use client";

import { ReactNode } from "react";
import { AuthProvider } from "./auth";
import { ReduxProvider } from "./redux";
import { UserInterfaceProvider } from "./context";
import { ThemeProvider } from "./theme";
import { RBACProvider } from "@/lib/rbac/context";
import { RouteManager } from "./router";
import { SWRProvider } from "./swr";
import { NotificationIntegrationProvider } from "./notification-integration";
import { ServiceWorkerProvider } from "@/components/common/service-worker";

interface ProvidersProps {
  children: ReactNode;
  session?: unknown;
}

export function Providers({ children, session }: ProvidersProps) {
  return (
    <ThemeProvider defaultTheme="system" storageKey="underscore-theme">
      <AuthProvider session={session}>
        <ReduxProvider>
          <RBACProvider>
            <UserInterfaceProvider>
              <SWRProvider>
                <ServiceWorkerProvider autoInitialize={true}>
                  <NotificationIntegrationProvider>
                    <RouteManager>{children}</RouteManager>
                  </NotificationIntegrationProvider>
                </ServiceWorkerProvider>
              </SWRProvider>
            </UserInterfaceProvider>
          </RBACProvider>
        </ReduxProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}

// Export individual providers for flexibility
export { AuthProvider } from "./auth";
export { ReduxProvider } from "./redux";
export { UserInterfaceProvider } from "./context";
export { ThemeProvider } from "./theme";
export { RBACProvider } from "@/lib/rbac/context";
export { NotificationIntegrationProvider } from "./notification-integration";
export { ServiceWorkerProvider } from "@/components/common/service-worker";
