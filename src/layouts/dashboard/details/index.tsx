"use client";

import React from "react";
import { useLayout } from "@/hooks/useLayout";
import { Listing as BasicListing } from "@/layouts/dashboard/details/basic";
import { Advanced as AdvancedListing } from "@/layouts/dashboard/details/advanced";
import type {
  ViewMode,
  DataType,
  ToolbarProps,
} from "@/layouts/dashboard/details/advanced";

// Types for the unified layout interface
export interface LayoutProps {
  children?: React.ReactNode;
  className?: string;
  // Basic layout props
  // Advanced layout props
  toolbar?: ToolbarProps;
}

export interface TableProps {
  id?: string;
  data?: any[];
  columns: Array<{
    key: string;
    label: string;
    render?: (item: any, index: number) => React.ReactNode;
    className?: string;
    dataType?: DataType; // For advanced layout
  }>;
  loading?: boolean;
  emptyState?: React.ReactNode;
  onRowClick?: (item: any) => void;
  className?: string;
  enableCheckboxes?: boolean;
  selectedRowIds?: string[];
  onSelectionChange?: (selectedIds: string[]) => void;
  getRowId?: (item: any) => string;
  getDataType?: (item: any, columnKey: string) => DataType; // For advanced layout
  contextMenuActions?: Array<{
    id: string;
    label: string;
    icon?: React.ReactNode;
    onClick: (item: any) => void;
    disabled?: boolean;
    separator?: boolean;
  }>; // For table row context menu
}

export interface GridProps {
  data?: any[];
  loading?: boolean;
  emptyState?: React.ReactNode;
  onItemClick?: (item: any) => void;
  className?: string;
  getDataType?: (item: any) => DataType; // For advanced layout
  renderCard?: (item: any, index: number) => React.ReactNode;
  contextMenuActions?: Array<{
    id: string;
    label: string;
    icon?: React.ReactNode;
    onClick: (item: any) => void;
    disabled?: boolean;
    separator?: boolean;
  }>; // For flow layout context menu
}

// Main Layout Renderer Component
function LayoutRenderer({ children, className = "", toolbar }: LayoutProps) {
  const { isAdvanced } = useLayout();

  if (isAdvanced) {
    return (
      <Container>
        <AdvancedListing
          className={className}
          toolbar={toolbar}
          hideStatistics={true}
        >
          {children}
        </AdvancedListing>
      </Container>
    );
  }

  // Default to basic layout
  return (
    <Container>
      <BasicListing className={className}>{children}</BasicListing>
    </Container>
  );
}

// Table Component Renderer
function TableRenderer({
  id,
  data,
  columns,
  loading,
  emptyState,
  onRowClick,
  className,
  enableCheckboxes,
  selectedRowIds,
  onSelectionChange,
  getRowId,
  getDataType,
  contextMenuActions,
}: TableProps) {
  const { isAdvanced } = useLayout();

  if (isAdvanced) {
    return (
      <AdvancedListing.Table
        id={id}
        data={data}
        columns={columns}
        loading={loading}
        emptyState={emptyState}
        onRowClick={onRowClick}
        className={className}
        enableCheckboxes={enableCheckboxes}
        selectedRowIds={selectedRowIds}
        onSelectionChange={onSelectionChange}
        getRowId={getRowId}
        getDataType={getDataType}
        contextMenuActions={contextMenuActions}
      />
    );
  }

  // Default to basic layout - filter out advanced-only props
  const basicColumns = columns.map(({ dataType, ...col }) => col);

  return (
    <BasicListing.Table
      id={id}
      data={data}
      columns={basicColumns}
      loading={loading}
      emptyState={emptyState}
      onRowClick={onRowClick}
      className={className}
      enableCheckboxes={enableCheckboxes}
      selectedRowIds={selectedRowIds}
      onSelectionChange={onSelectionChange}
      getRowId={getRowId}
    />
  );
}

// Grid Component Renderer
function GridRenderer({
  data,
  loading,
  emptyState,
  onItemClick,
  className,
  getDataType,
  renderCard,
  contextMenuActions,
}: GridProps) {
  const { isAdvanced } = useLayout();

  if (isAdvanced) {
    return (
      <AdvancedListing.Grid
        data={data}
        loading={loading}
        emptyState={emptyState}
        onItemClick={onItemClick}
        className={className}
        getDataType={getDataType}
        renderCard={renderCard}
        contextMenuActions={contextMenuActions}
      />
    );
  }

  // Default to basic layout - use Cards component
  return (
    <BasicListing.Cards
      data={data}
      loading={loading}
      emptyState={emptyState}
      onItemClick={onItemClick}
      className={className}
      renderCard={renderCard}
    />
  );
}

// Flow Component Renderer (Advanced layout only)
function FlowRenderer({
  data,
  loading,
  emptyState,
  onItemClick,
  className,
  getDataType,
  contextMenuActions,
}: GridProps) {
  const { isAdvanced } = useLayout();

  if (isAdvanced) {
    return (
      <AdvancedListing.Flow
        data={data}
        loading={loading}
        emptyState={emptyState}
        onItemClick={onItemClick}
        className={className}
        getDataType={getDataType}
        contextMenuActions={contextMenuActions}
      />
    );
  }

  // Flow view is only available in advanced layout
  // Fall back to grid for basic layout
  return (
    <BasicListing.Cards
      data={data}
      loading={loading}
      emptyState={emptyState}
      onItemClick={onItemClick}
      className={className}
    />
  );
}

// Header Component Renderer
function HeaderRenderer(props: any) {
  const { isAdvanced } = useLayout();

  if (isAdvanced) {
    return <AdvancedListing.Header {...props} />;
  }

  // Default to basic layout
  return <BasicListing.Header {...props} />;
}

// Filters Component Renderer (Basic layout only)
function FiltersRenderer(props: any) {
  const { isBasic } = useLayout();

  // Filters are only available in basic layout
  if (isBasic) {
    return <BasicListing.Filters {...props} />;
  }

  // For advanced layout, filters are handled by toolbar
  return null;
}

// Controls Component Renderer (Basic layout only)
function ControlsRenderer(props: any) {
  const { isBasic } = useLayout();

  // Controls are only available in basic layout
  if (isBasic) {
    return <BasicListing.Controls {...props} />;
  }

  // For advanced layout, controls are handled by toolbar
  return null;
}

// Toolbar Component Renderer (Advanced layout only)
function ToolbarRenderer(props: ToolbarProps) {
  const { isAdvanced } = useLayout();

  // Toolbar is only available in advanced layout
  if (isAdvanced) {
    return <AdvancedListing.Toolbar {...props} />;
  }

  // For basic layout, toolbar functionality is split across Header, Filters, and Controls
  return null;
}

// Utility function to get icon for data type (Advanced layout only)
function getIconForDataType(type: DataType, size?: number) {
  const { isAdvanced } = useLayout();

  if (isAdvanced) {
    return AdvancedListing.getIconForDataType(type, size);
  }

  // Basic layout doesn't have dynamic icons
  return null;
}

// Container component with fixed padding
function Container({
  children,
  className = "",
}: {
  children: React.ReactNode;
  className?: string;
}) {
  return <div className={`px-5 py-6 ${className}`}>{children}</div>;
}

// Export types
export type { ViewMode, DataType, ToolbarProps };

// Utility function to check if statistics should be shown in main view
function shouldShowStatisticsInMainView() {
  const { isBasic } = useLayout();
  return isBasic;
}

// Export the unified layout components with Container
export const Listing = Object.assign(LayoutRenderer, {
  Table: TableRenderer,
  Grid: GridRenderer,
  Cards: GridRenderer, // Alias for Grid
  Flow: FlowRenderer,
  Header: HeaderRenderer,
  Filters: FiltersRenderer,
  Controls: ControlsRenderer,
  Toolbar: ToolbarRenderer,
  Container,
  getIconForDataType,
  shouldShowStatisticsInMainView,
});
