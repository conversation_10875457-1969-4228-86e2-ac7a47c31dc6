"use client";

import use<PERSON><PERSON> from "swr";

import { useRouter, useParams } from "next/navigation";

import { fetcher } from "@/lib/common/requests";
import { toast } from "sonner";
import { useCallback, useEffect, useState, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { RootState, AppDispatch } from "@/store";
import {
  fetchProposal,
  createProposal,
  updateProposal,
  deleteProposal,
  searchProposals,
} from "@/store/actions/proposals";
import {
  selectCurrentProposal,
  selectIsCreating,
  selectIsUpdating,
  selectIsDeleting,
  selectError,
  clearError,
  setCreating,
  setUpdating,
  setDeleting,
  setError,
  setCurrentProposal,
} from "@/store/slices/proposal";
import type {
  CreateProposal,
  UpdateProposal,
  Proposal as ApiProposal,
} from "@/lib/api/validators/schemas/proposal";
import type { Proposal, ProposalStats } from "@/data/proposals-mock";
import { usePagination } from "./usePagination";

// Convert API Proposal to UI Proposal for display
const adaptApiProposalToUI = (apiProposal: ApiProposal): Proposal => {
  const statusMap: Record<string, Proposal["status"]> = {
    created: "draft",
    pending: "pending",
    approved: "approved",
    rejected: "rejected",
    completed: "completed",
  };

  return {
    id: apiProposal.id,
    name: apiProposal.name,
    description: apiProposal.description || "",
    client: apiProposal.client?.name || "",
    status: statusMap[apiProposal.status] || "draft",
    budgetType: apiProposal.fixed_budget ? "fixed" : "milestone",
    fixedBudget: apiProposal.fixed_budget,
    milestones: apiProposal.milestones || [],
    totalBudget: apiProposal.total_budget || 0,
    duration: apiProposal.duration || 0,
    createdDate: new Date(apiProposal.createdAt)?.toISOString(),
    lastModified: new Date(apiProposal.updatedAt)?.toISOString(),
    attachments: apiProposal.links || [],
    agreed_to_terms_and_conditions: apiProposal.agreed_to_terms_and_conditions,
  };
};

// Convert UI Proposal to API Proposal for submission
const adaptUIProposalToAPI = (uiProposal: Proposal): Partial<ApiProposal> => {
  const statusMap: Record<Proposal["status"], ApiProposal["status"]> = {
    draft: "created",
    pending: "submitted",
    approved: "agreed",
    rejected: "closed",
    completed: "completed",
  };

  return {
    id: uiProposal.id,
    name: uiProposal.name,
    description: uiProposal.description,
    status: statusMap[uiProposal.status] || "created",
    links: uiProposal.attachments || [],
    milestones: uiProposal.milestones || [],
    fixed_budget: uiProposal.fixedBudget,
    total_budget: uiProposal.totalBudget || 0,
    duration: uiProposal.duration || 0,
    agreed_to_terms_and_conditions: uiProposal.agreed_to_terms_and_conditions,
  };
};

export function useProposal() {
  const router = useRouter();
  const { slug } = useParams();

  const dispatch = useDispatch<AppDispatch>();

  const [proposals, setProposals] = useState<Proposal[]>([]);
  const [filteredProposals, setFilteredProposals] = useState<ApiProposal[]>([]);
  const [searchTerm, setSearchTerm] = useState("");

  // Debouncer for API calls
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize pagination hook for proposals table
  const paginationHook = usePagination("proposals-table");

  // Redux selectors for operation states
  const currentProposal = useSelector((state: RootState) =>
    selectCurrentProposal(state)
  );
  const isCreating = useSelector((state: RootState) => selectIsCreating(state));
  const isUpdating = useSelector((state: RootState) => selectIsUpdating(state));
  const isDeleting = useSelector((state: RootState) => selectIsDeleting(state));
  const error = useSelector((state: RootState) => selectError(state));

  // SWR hooks for data fetching
  const {
    data: proposalsData,
    error: proposalsError,
    isLoading: isLoadingProposals,
    mutate: mutateProposals,
  } = useSWR("proposal", fetcher);

  const {
    data: statisticsData,
    error: statisticsError,
    isLoading: isLoadingStats,
    mutate: mutateStatistics,
  } = useSWR("proposal/statistics", fetcher);

  // Extract data from SWR responses
  const serverPagination = proposalsData?.pagination;
  const statistics: ProposalStats | null = statisticsData?.data || null;
  const isLoading = isLoadingProposals;

  // Helper function to adapt API proposals to UI format
  function setUiProposals(apiProposals: ApiProposal[]) {
    if (!apiProposals || apiProposals.length === 0) return;
    const uiProposals = apiProposals.map(adaptApiProposalToUI);
    setProposals(uiProposals);
  }

  useEffect(() => {
    // Reset Proposals Data on search term clear
    if (!searchTerm) {
      setUiProposals(proposalsData?.data);
    }
  }, [searchTerm]);

  useEffect(() => {
    if (filteredProposals) {
      setUiProposals(filteredProposals);
    }
  }, [filteredProposals]);

  useEffect(() => {
    if (proposalsData) {
      setUiProposals(proposalsData.data);
    }
  }, [proposalsData]);

  // Initialize and update pagination based on fetched data
  useEffect(() => {
    if (proposalsData) {
      // Initialize pagination if not already done
      if (paginationHook.pagination.total === 0) {
        paginationHook.initPagination({
          initialPage: serverPagination?.page || 1,
          initialLimit: serverPagination?.limit || 10,
          initialOrderBy: { createdAt: "desc" },
        });
      }

      // Update pagination with server data when available
      if (serverPagination) {
        paginationHook.updateTotal(serverPagination.total);
        // Update other pagination metadata if needed
        if (serverPagination.page !== paginationHook.pagination.page) {
          paginationHook.setPage(serverPagination.page);
        }
        if (serverPagination.limit !== paginationHook.pagination.limit) {
          paginationHook.setLimit(serverPagination.limit);
        }
      }
    }
  }, [proposalsData, serverPagination, paginationHook]);

  // Handle SWR errors
  const swrError = proposalsError || statisticsError;
  if (swrError && !error) {
    dispatch(setError(swrError.message));
  }

  // API operations
  const create = useCallback(
    async (data: CreateProposal) => {
      try {
        dispatch(setCreating(true));
        dispatch(clearError());

        const result = await dispatch(createProposal(data)).unwrap();

        // Show success toast
        toast.success("Proposal created successfully");

        // Refresh data after creation
        await mutateProposals();
        await mutateStatistics();

        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to create proposal";
        toast.error(errorMessage);
        dispatch(setError(errorMessage));
        throw error;
      } finally {
        dispatch(setCreating(false));
      }
    },
    [dispatch, mutateProposals, mutateStatistics]
  );

  const update = useCallback(
    async (data: UpdateProposal) => {
      try {
        dispatch(setUpdating(true));
        dispatch(clearError());

        const result = await dispatch(updateProposal(data)).unwrap();

        // Show success toast
        toast.success("Proposal updated successfully");

        // Update current proposal if it's the one being updated
        if (currentProposal?.id === data.id) {
          dispatch(setCurrentProposal(result));
        }

        // Refresh data after update
        await mutateProposals();
        await mutateStatistics();

        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to update proposal";
        toast.error(errorMessage);
        dispatch(setError(errorMessage));
        throw error;
      } finally {
        dispatch(setUpdating(false));
      }
    },
    [dispatch, currentProposal, mutateProposals, mutateStatistics]
  );

  const remove = useCallback(
    async (id: string) => {
      try {
        dispatch(setDeleting(true));
        dispatch(clearError());

        await dispatch(deleteProposal(id)).unwrap();

        // Show success toast
        toast.success("Proposal deleted successfully");

        router.push(`/${slug}/proposals`);

        // Clear current proposal if it's the one being deleted
        if (currentProposal?.id === id) {
          dispatch(setCurrentProposal(null));
        }

        // Refresh data after deletion
        await mutateProposals();
        await mutateStatistics();
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to delete proposal";
        toast.error(errorMessage);
        dispatch(setError(errorMessage));
        throw error;
      } finally {
        dispatch(setDeleting(false));
      }
    },
    [dispatch, currentProposal, mutateProposals, mutateStatistics]
  );

  // Utility functions
  const fetchById = useCallback(
    async (id: string) => {
      try {
        const result = await dispatch(fetchProposal(id)).unwrap();
        dispatch(setCurrentProposal(result));
        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to fetch proposal";
        dispatch(setError(errorMessage));
        throw error;
      }
    },
    [dispatch]
  );

  // Search Proposals
  const searchProposalsFunction = useCallback(
    async (searchParams: { query?: string; page?: number; limit?: number }) => {
      try {
        const query = searchParams.query?.trim() || "";

        // If no query, reset to show all proposals
        if (!query) {
          setFilteredProposals([]);
          setSearchTerm("");
          return {
            success: true,
            data: proposals,
            pagination: null,
            searchQuery: "",
          };
        }

        // Update search term
        setSearchTerm(query);

        // First, try to filter from loaded data if available
        if (proposalsData?.data && Array.isArray(proposalsData.data)) {
          const loadedProposals = proposalsData.data;

          // Perform local filtering
          const localFilteredProposals = loadedProposals.filter(
            (proposal: any) => {
              const searchLower = query.toLowerCase();
              return (
                proposal.name?.toLowerCase().includes(searchLower) ||
                proposal.description?.toLowerCase().includes(searchLower) ||
                proposal.account?.user?.name
                  ?.toLowerCase()
                  .includes(searchLower) ||
                proposal.account?.user?.email
                  ?.toLowerCase()
                  .includes(searchLower) ||
                proposal.status?.toLowerCase().includes(searchLower)
              );
            }
          );

          // If we have local results, use them
          if (localFilteredProposals.length > 0 || loadedProposals.length > 0) {
            setFilteredProposals(localFilteredProposals);
            return {
              success: true,
              data: localFilteredProposals,
              pagination: null, // Local filtering doesn't have server pagination
              searchQuery: query,
              source: "local", // Indicate this was a local search
            };
          }
        }

        // If no local data available or no results, perform debounced API call
        return new Promise((resolve) => {
          // Clear existing timer
          if (debounceTimerRef.current) {
            clearTimeout(debounceTimerRef.current);
          }

          // Set new timer for 1 second delay
          debounceTimerRef.current = setTimeout(async () => {
            try {
              const result = await dispatch(searchProposals(searchParams));

              // Handle response based on Redux action result pattern
              if (result.type.endsWith("/fulfilled")) {
                const payload = result.payload as any;
                setFilteredProposals(payload.proposals);
                resolve({
                  success: true,
                  data: payload.proposals,
                  pagination: payload.pagination,
                  searchQuery: payload.searchQuery,
                  source: "api", // Indicate this was an API search
                });
              } else if (result.type.endsWith("/rejected")) {
                const errorMessage =
                  (result.payload as any)?.message ||
                  "Failed to search proposals";
                toast.error(errorMessage);
                resolve({ success: false, error: errorMessage });
              }
              resolve(result);
            } catch (apiError) {
              const errorMessage =
                apiError instanceof Error
                  ? apiError.message
                  : "Failed to search proposals";
              toast.error(errorMessage);
              resolve({ success: false, error: errorMessage });
            }
          }, 1400); // 1.4 second delay
        });
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to search proposals";
        toast.error(errorMessage);
        throw error;
      }
    },
    [dispatch, proposals, proposalsData, setSearchTerm]
  );

  const clearErrorState = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  // Refresh data functions using SWR mutate
  const refreshProposals = useCallback(() => {
    mutateProposals();
  }, [mutateProposals]);

  const refreshStatistics = useCallback(() => {
    mutateStatistics();
  }, [mutateStatistics]);

  const refreshData = useCallback(() => {
    mutateProposals();
    mutateStatistics();
  }, [mutateProposals, mutateStatistics]);

  // Initialize proposals data using SWR
  const initializeProposals = useCallback(() => {
    refreshData();
  }, [refreshData]);

  // Cleanup debounce timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  return {
    // State
    proposals,
    currentProposal,
    statistics,
    searchTerm,
    isLoading,
    isCreating,
    isUpdating,
    isDeleting,
    isLoadingStats,
    error,

    // Pagination
    pagination: paginationHook,

    // Data refresh actions (SWR-based)
    refreshProposals,
    refreshStatistics,
    refreshData,

    // Actions
    create,
    fetchById,
    update,
    remove,
    searchProposals: searchProposalsFunction,
    initializeProposals,
    setSearchTerm,
    clearError: clearErrorState,

    // Data transformation utilities
    adaptUIProposalToAPI,

    // SWR utilities
    mutateProposals,
    mutateStatistics,

    // Computed values
    hasProposals: proposals.length > 0,
    isAnyLoading:
      isLoading || isCreating || isUpdating || isDeleting || isLoadingStats,
  };
}
