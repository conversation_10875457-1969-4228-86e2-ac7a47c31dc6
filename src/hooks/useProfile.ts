"use client";

import { useCallback } from "react";

import { toast } from "sonner";
import { useDispatch } from "react-redux";
import { useSession } from "next-auth/react";

import { useAuth } from "@/hooks/useAuth";
import type { Profile } from "@/store/actions/auth";
import { updateUserProfile as updateProfileAction } from "@/store/actions/auth";

/**
 * Hook for managing user profiles
 * Decoupled from RBAC system for focused profile management
 */
export function useProfile() {
  const dispatch = useDispatch();
  const { data: currentSession, update: updateCurrentSession } = useSession();
  const { user: currentUser, isLoading: isAuthLoading } = useAuth();

  const profile: any = currentUser?.profile;

  const updateUserProfile = useCallback(
    async (profileData: Partial<Profile>) => {
      try {
        const result = await dispatch(
          updateProfileAction(profileData) as any
        ).unwrap();

        // Update session data with new profile
        await updateCurrentSession({
          ...currentSession,
          profile: result,
        });

        // Handle toast notifications based on result
        toast.success("Profile updated successfully");
        return { success: true, data: result };
        return result;
      } catch (error: any) {
        const errorMessage = error?.message || "Failed to update profile";
        toast.error(errorMessage);
      }
    },
    []
  );

  const refreshProfile = useCallback(async () => {
    try {
      // Trigger a profile refresh if needed
      // This could be implemented based on your auth system
      return { success: true };
    } catch (error: any) {
      const errorMessage = error?.message || "Failed to refresh profile";
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  }, []);

  const validateProfile = useCallback((profileData: Partial<Profile>) => {
    const errors: string[] = [];

    if (profileData?.email && !/\S+@\S+\.\S+/.test(profileData?.email)) {
      errors.push("Invalid email format");
    }

    if (profileData?.firstName && profileData?.firstName?.trim()?.length < 2) {
      errors.push("First name must be at least 2 characters");
    }

    if (profileData?.lastName && profileData?.lastName?.trim()?.length < 2) {
      errors.push("Last name must be at least 2 characters");
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }, []);

  return {
    // Profile data
    profile,
    currentUser,
    hasProfile: !!profile,

    // State
    isLoading: isAuthLoading,
    error: null,

    // Actions
    updateUserProfile,
    upsertProfile: updateUserProfile, // Alias for backward compatibility
    refreshProfile,
    validateProfile,

    // Computed properties
    fullName: profile
      ? `${profile?.firstName || ""} ${profile?.lastName || ""}`.trim()
      : "",
    initials: profile
      ? `${profile?.firstName?.[0] || ""}${
          profile?.lastName?.[0] || ""
        }`.toUpperCase()
      : "",
    displayName:
      profile?.displayName ||
      profile?.firstName ||
      currentUser?.email ||
      "User",
  };
}
