# Unified Notification Library Guide

This guide explains how to use the unified notification library that combines email, in-app (toast), and push notifications based on user preferences.

## Overview

The notification library provides a single interface to send notifications across multiple channels:

- **Email** - Traditional email notifications using templates
- **In-App** - Toast notifications using Sonner
- **Push** - Browser push notifications using service workers

## Quick Start

### Basic Usage

```typescript
import { notify, notifyWithChannels } from "@/lib/notifications";

// Send notification based on user preferences
await notify({
  type: "newMessages",
  title: "New Message",
  message: "You have a new message from <PERSON>",
  data: { userId: "user-123" },
});

// Send with explicit channel control
await notifyWithChannels(
  "contractChanges",
  "Contract Updated",
  "Service agreement was updated",
  true, // email
  true, // inApp
  false // push
);
```

### Setting User Preferences

```typescript
import { notificationManager } from "@/lib/notifications";

// Set user preferences (typically from useNotifications hook)
notificationManager.setPreferences({
  email: {
    newMessages: true,
    contractChanges: true,
    proposalUpdates: true,
    userActivity: false,
    systemAlerts: true,
    weeklyDigest: true,
  },
  push: {
    newMessages: true,
    contractChanges: true,
    proposalUpdates: false,
    userActivity: false,
    systemAlerts: true,
  },
  inApp: {
    newMessages: true,
    contractChanges: true,
    proposalUpdates: true,
    userActivity: true,
    systemAlerts: true,
    roleChanges: true,
  },
});
```

## Integration with Hooks

### useNotifications Integration

```typescript
// In your component or hook
import { useNotifications } from "@/hooks/useNotifications";
import { notificationManager } from "@/lib/notifications";
import { useEffect } from "react";

function MyComponent() {
  const { preferences } = useNotifications();

  // Set preferences when they load
  useEffect(() => {
    if (preferences) {
      notificationManager.setPreferences(preferences);
    }
  }, [preferences]);

  // Now notifications will respect user preferences
  const handleNewMessage = async () => {
    await notify({
      type: "newMessages",
      title: "New Message",
      message: "You have a new message",
    });
  };
}
```

### useChat Integration

```typescript
// In your chat hook
import { notify } from "@/lib/notifications";

export function useChat() {
  // ... existing chat logic

  const sendMessage = async (message: string) => {
    // ... send message logic

    // Notify other users
    await notify({
      type: "newMessages",
      title: `New message from ${currentUser.name}`,
      message: message,
      data: {
        userId: recipientId,
        entityType: "message",
        actionUrl: `/dashboard/chat/${roomId}`,
      },
      emailData: {
        to: recipientEmail,
        templateData: {
          senderName: currentUser.name,
          message,
          roomName,
        },
      },
      pushData: {
        tag: "new-message",
        actions: [
          { action: "reply", title: "Reply" },
          { action: "view", title: "View" },
        ],
      },
      inAppData: {
        variant: "info",
        action: {
          label: "View Chat",
          onClick: () => navigateToRoom(roomId),
        },
      },
    });
  };
}
```

## Notification Types

The library supports these notification types that map to user preferences:

- `newMessages` - Chat messages and communications
- `contractChanges` - Contract status and modifications
- `proposalUpdates` - Proposal status changes
- `userActivity` - User actions and activity
- `systemAlerts` - System-wide alerts and announcements
- `roleChanges` - Role and permission changes
- `weeklyDigest` - Weekly summary emails

## Channel-Specific Configuration

### Email Notifications

```typescript
await notify({
  type: "contractChanges",
  title: "Contract Updated",
  message: "Your contract status has changed",
  emailData: {
    to: "<EMAIL>",
    templateData: {
      contractTitle: "Website Development",
      clientName: "Acme Corp",
      totalValue: 50000,
      status: "Active",
    },
  },
});
```

### Push Notifications

```typescript
await notify({
  type: "systemAlerts",
  title: "System Maintenance",
  message: "Scheduled maintenance in 1 hour",
  pushData: {
    tag: "maintenance-alert",
    requireInteraction: true,
    actions: [
      { action: "view", title: "View Details" },
      { action: "dismiss", title: "Dismiss" },
    ],
  },
});
```

### In-App Notifications

```typescript
await notify({
  type: "contractChanges",
  title: "Contract Updated",
  message: "Service agreement has been updated",
  inAppData: {
    variant: "success",
    duration: 5000,
    action: {
      label: "View Contract",
      onClick: () => router.push("/contracts/123"),
    },
  },
});
```

## Advanced Usage

### Bulk Notifications

```typescript
import { sendBulkNotification } from "@/lib/notifications/examples";

// Send to multiple users
const result = await sendBulkNotification(
  ["user1", "user2", "user3"],
  "systemAlerts",
  "System Update",
  "The system has been updated with new features"
);

console.log(`Sent to ${result.successful}/${result.total} users`);
```

### Error Handling and Retry

```typescript
import { sendNotificationWithRetry } from "@/lib/notifications/examples";

const result = await sendNotificationWithRetry(
  {
    type: "contractChanges",
    title: "Important Contract Update",
    message: "Please review your contract changes",
  },
  3, // max retries
  1000 // retry delay in ms
);

if (!result.success) {
  console.error(`Failed after ${result.attempts} attempts`);
}
```

### Custom Hook Integration

```typescript
import { useIntegratedNotifications } from "@/lib/notifications/examples";

function MyComponent() {
  const {
    initializeWithUserPreferences,
    notifyNewMessage,
    notifyDocumentUpdate,
  } = useIntegratedNotifications();

  // Initialize with user preferences
  useEffect(() => {
    if (userPreferences) {
      initializeWithUserPreferences(userPreferences);
    }
  }, [userPreferences]);

  // Use simplified notification methods
  const handleNewMessage = () => {
    notifyNewMessage("John Doe", "Hello there!", "user-123");
  };
}
```

## Best Practices

### 1. Respect User Preferences

Always use the preference-based `notify()` function unless you have a specific reason to override channels.

### 2. Provide Meaningful Actions

Include action buttons in notifications that help users take immediate action.

### 3. Use Appropriate Variants

Choose the right in-app notification variant:

- `success` - Successful operations
- `info` - General information
- `warning` - Important notices
- `destructive` - Errors or critical issues

### 4. Handle Errors Gracefully

Always handle notification failures gracefully and provide fallbacks.

### 5. Batch Similar Notifications

Avoid spamming users with multiple similar notifications. Use batching or debouncing.

### 6. Test Across Channels

Test your notifications across all channels to ensure consistent messaging.

## Troubleshooting

### Notifications Not Sending

1. Check if user preferences are set correctly
2. Verify email templates exist for email notifications
3. Ensure push notifications are properly configured
4. Check browser console for errors

### Email Notifications Failing

1. Verify email service configuration
2. Check if email templates exist
3. Ensure recipient email is valid
4. Check email service logs

### Push Notifications Not Working

1. Ensure service worker is registered
2. Check if user granted notification permission
3. Verify VAPID keys are configured
4. Check if user is subscribed to push notifications

### In-App Notifications Not Showing

1. Verify Sonner toast provider is configured
2. Check if notifications are being blocked by user settings
3. Ensure proper variant is being used

## API Reference

### Main Functions

- `notify(payload, channelOverride?)` - Send notification based on preferences
- `notifyWithChannels(type, title, message, email, inApp, push, additionalData?)` - Send with explicit channels
- `notificationManager.setPreferences(preferences)` - Set user preferences

### Types

- `NotificationPayload` - Complete notification configuration
- `NotificationChannels` - Channel enable/disable flags
- `NotificationPreferences` - User preference structure
- `NotificationType` - Available notification types

See the examples file for more detailed usage patterns and integration examples.
