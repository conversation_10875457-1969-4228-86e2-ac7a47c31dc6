# Socket.IO Implementation Steps

This document outlines the step-by-step process of how Socket.IO was implemented in the application.

## Implementation Timeline

### Phase 1: Core Infrastructure Setup

#### 1. Package Installation
```bash
npm install socket.io socket.io-client
npm install --save-dev @types/socket.io
```

#### 2. Server-Side Socket Implementation (`src/lib/socket/server.ts`)

**Step 1: Basic Server Setup**
```typescript
import { Server as HTTPServer } from "http";
import { Server as SocketIOServer, Socket } from "socket.io";

let io: SocketIOServer | null = null;
const activeUsers = new Map<string, Set<string>>();
const socketToUser = new Map<string, string>();

export function initializeSocketServer(httpServer: HTTPServer): SocketIOServer {
  if (io) return io;
  
  io = new SocketIOServer(httpServer, {
    cors: {
      origin: process.env.NEXTAUTH_URL || "http://localhost:3000",
      methods: ["GET", "POST"],
      credentials: true,
    },
    path: "/api/socket",
  });
  
  return io;
}
```

**Step 2: Connection Handling**
```typescript
io.on("connection", async (socket: Socket) => {
  console.log(`Socket connected: ${socket.id}`);
  
  // Authentication handler
  socket.on("authenticate", async (data: { sessionToken?: string }) => {
    const userId = data.sessionToken;
    if (userId) {
      // Register user connection
      if (!activeUsers.has(userId)) {
        activeUsers.set(userId, new Set());
      }
      activeUsers.get(userId)!.add(socket.id);
      socketToUser.set(socket.id, userId);
      socket.join(`user:${userId}`);
      socket.emit("authenticated", { success: true, userId });
    }
  });
  
  // Context management
  socket.on("join_context", (data: { contextType: string; contextId: string }) => {
    const roomName = `${data.contextType}:${data.contextId}`;
    socket.join(roomName);
    socket.emit("joined_context", { ...data, room: roomName });
  });
  
  // Cleanup on disconnect
  socket.on("disconnect", () => {
    const userId = socketToUser.get(socket.id);
    if (userId) {
      const userSockets = activeUsers.get(userId);
      if (userSockets) {
        userSockets.delete(socket.id);
        if (userSockets.size === 0) {
          activeUsers.delete(userId);
        }
      }
      socketToUser.delete(socket.id);
    }
  });
});
```

#### 3. Client-Side Socket Implementation (`src/lib/socket/client.ts`)

**Step 1: Connection Management**
```typescript
import { io, Socket } from "socket.io-client";

let socket: Socket | null = null;
const eventListeners = new Map<string, Set<Function>>();

export function initializeSocket(userId?: string): Socket {
  if (socket?.connected) return socket;
  
  if (socket) socket.disconnect();
  
  socket = io(process.env.NEXT_PUBLIC_SOCKET_URL || window.location.origin, {
    path: "/api/socket",
    transports: ["websocket", "polling"],
    autoConnect: true,
  });
  
  // Auto-authenticate
  if (userId && socket) {
    socket.emit("authenticate", { sessionToken: userId });
  }
  
  return socket;
}
```

**Step 2: Event Management**
```typescript
export function addEventListener<K extends keyof SocketClientEvents>(
  event: K,
  listener: SocketClientEvents[K]
): void {
  if (!eventListeners.has(event)) {
    eventListeners.set(event, new Set());
  }
  eventListeners.get(event)!.add(listener);
}

export function joinContext(contextType: string, contextId: string): void {
  if (!socket) return;
  socket.emit("join_context", { contextType, contextId });
}
```

### Phase 2: React Integration

#### 4. React Hook Implementation (`src/hooks/useSocket.ts`)

**Step 1: Hook Structure**
```typescript
export function useSocket(options: UseSocketOptions = {}) {
  const { data: session } = useSession();
  const { autoConnect = true, contexts = [], onNotification } = options;
  
  const currentContexts = useRef<Set<string>>(new Set());
  
  const connect = useCallback(() => {
    if (!session?.user?.id) return null;
    return initializeSocket(session.user.id);
  }, [session?.user?.id]);
  
  // Setup event listeners and context management
  useEffect(() => {
    if (!autoConnect || !session?.user?.id) return;
    
    const socket = connect();
    if (!socket) return;
    
    // Notification handler with user filtering
    const notificationListener = (data: NotificationSocketData) => {
      if (data.userId === session.user.id) {
        onNotification?.(data);
      }
    };
    
    addEventListener("notification", notificationListener);
    
    return () => {
      removeEventListener("notification", notificationListener);
      disconnect();
    };
  }, [autoConnect, session?.user?.id, onNotification]);
}
```

### Phase 3: Service Integration

#### 5. Notification Service Enhancement (`src/lib/api/services/notification.ts`)

**Step 1: Socket Integration**
```typescript
import { 
  sendNotificationToUser, 
  sendNotificationToContext,
  NotificationSocketData 
} from "@/lib/socket/server";

// Enhanced createNotification method
async createNotification(data: NotificationData): Promise<ServiceResponse<any>> {
  const result = await this.executeOperation(async () => {
    const notification: any = await this.createRecord({
      data: { ...validatedData, data: JSON.stringify(validatedData.data) },
      include: { user: { select: { id: true, name: true, email: true } } }
    });

    // Send real-time notification via Socket.IO
    if (notification) {
      const socketData: NotificationSocketData = {
        id: notification.id,
        title: notification.title,
        message: notification.message,
        type: notification.type,
        category: notification.category,
        userId: notification.user.id,
        data: validatedData.data,
        priority: notification.priority,
        createdAt: notification.createdAt,
      };

      sendNotificationToUser(notification.user.id, socketData);
    }

    return notification;
  }, "createNotification");
}
```

**Step 2: Context-Based Methods**
```typescript
async createAndSendContextNotification(
  data: NotificationData,
  contextType: string,
  contextId: string,
  excludeUserId?: string
): Promise<ServiceResponse<any>> {
  const result = await this.createNotification(data);
  
  if (result.success && result.data) {
    const socketData: NotificationSocketData = { /* ... */ };
    sendNotificationToContext(contextType, contextId, socketData, excludeUserId);
  }
  
  return result;
}
```

#### 6. Chat Service Integration (`src/lib/api/services/chat.ts`)

**Step 1: Import Notification Service**
```typescript
import { notificationService } from "./notification";
```

**Step 2: Message Notifications**
```typescript
// In sendMessage method
if (messageToCreate.roomId) {
  await this.createMessageNotifications(
    messageToCreate.roomId,
    newMessage.id,
    messageToCreate.content || "New message",
    currentAccount
  );
}

// Helper method
private async createMessageNotifications(
  roomId: string,
  messageId: string,
  messageContent: string,
  sender: any
): Promise<void> {
  // Get room members except sender
  const roomMembers = await this.findManyRecords({
    where: { roomId: roomId, accountId: { not: sender.id } },
    include: { account: { select: { id: true, name: true, email: true } } }
  });

  // Create notifications for each member
  for (const member of roomMembers as any[]) {
    await notificationService.createAndSendContextNotification(
      {
        title: `New message in ${room.name || "Chat Room"}`,
        message: `${sender.name}: ${messageContent.substring(0, 100)}...`,
        type: "chat",
        category: "inApp",
        userId: member.account.id,
        data: { roomId, messageId, actionUrl: `/chat/${roomId}` }
      },
      "chat",
      roomId,
      sender.id
    );
  }
}
```

### Phase 4: Client-Side Integration

#### 7. Enhanced useNotifications Hook

**Step 1: Socket Integration**
```typescript
export function useNotifications() {
  const [realtimeNotifications, setRealtimeNotifications] = useState<any[]>([]);
  
  // Socket integration for real-time notifications
  const { isConnected, joinContext, leaveContext } = useSocket({
    autoConnect: true,
    onNotification: (notification) => {
      // Add to real-time notifications
      setRealtimeNotifications(prev => [notification, ...prev]);
      
      // Show toast notification
      toast.success(notification.title, {
        description: notification.message,
        action: notification.data?.actionUrl ? {
          label: "View",
          onClick: () => window.location.href = notification.data.actionUrl
        } : undefined,
      });
      
      // Refresh SWR cache
      mutateNotifications();
    }
  });
  
  // Combine real-time and fetched notifications
  const notifications = [...realtimeNotifications, ...baseNotifications]
    .filter((notification, index, self) => 
      index === self.findIndex(n => n.id === notification.id)
    );
}
```

### Phase 5: API Route Setup

#### 8. Socket API Route (`src/app/api/socket/route.ts`)

```typescript
import { initializeSocketServer, getSocketServer } from "@/lib/socket/server";

export async function GET(request: NextRequest) {
  const socketServer = getSocketServer();
  
  return NextResponse.json({
    success: true,
    initialized: !!socketServer,
    message: socketServer 
      ? "Socket.IO server is running" 
      : "Socket.IO server not initialized"
  });
}
```

## Key Implementation Decisions

### 1. Context-Based Architecture
- **Rooms**: `chat:roomId`, `contract:contractId`, `user:userId`
- **Benefits**: Efficient message routing, scalable user management
- **Usage**: Users join contexts to receive relevant notifications

### 2. User State Management
- **Active Users Map**: Track online users and their socket connections
- **Socket-to-User Mapping**: Quick user lookup from socket ID
- **Cleanup**: Automatic cleanup on disconnect

### 3. Notification Flow
1. **Database Persistence** - Save notification to database
2. **Socket Delivery** - Send via Socket.IO to relevant contexts
3. **Client Reception** - React hooks receive and process notifications
4. **UI Updates** - Toast notifications and data refresh

### 4. Error Handling Strategy
- **Non-blocking**: Socket failures don't break main functionality
- **Graceful Degradation**: Fall back to polling if Socket.IO fails
- **Comprehensive Logging**: All operations logged for debugging

## Environment Setup

```env
# Required for Socket.IO
NEXT_PUBLIC_SOCKET_URL=http://localhost:3000
NEXTAUTH_URL=http://localhost:3000

# Production
NEXT_PUBLIC_SOCKET_URL=https://yourdomain.com
NEXTAUTH_URL=https://yourdomain.com
```

## Testing the Implementation

### 1. Connection Test
```typescript
// Check if socket connects
const socket = initializeSocket("test-user-id");
console.log("Connected:", socket.connected);
```

### 2. Notification Test
```typescript
// Send test notification
await notificationService.createAndSendNotification({
  title: "Test Notification",
  message: "This is a test",
  type: "chat",
  category: "inApp",
  userId: "target-user-id"
});
```

### 3. Context Test
```typescript
// Join context and send context notification
joinContext("chat", "room-123");
await notificationService.createAndSendContextNotification(
  notificationData,
  "chat",
  "room-123"
);
```

This implementation provides a robust, scalable real-time notification system that integrates seamlessly with the existing application architecture.
