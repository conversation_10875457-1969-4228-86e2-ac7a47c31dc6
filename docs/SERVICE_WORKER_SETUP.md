# Service Worker & Push Notifications Setup

This guide explains how to set up and use the service worker with push notifications in your application.

## Overview

The service worker implementation includes:
- **Workbox** for caching strategies and offline functionality
- **Push Notifications** with VAPID authentication
- **Background Sync** for offline actions
- **Automatic Updates** with user notification

## Quick Setup

### 1. Generate VAPID Keys

First, generate VAPID keys for push notifications:

```bash
node scripts/generate-vapid-keys.js
```

Add the generated keys to your `.env.local` file:

```env
VAPID_PUBLIC_KEY="your-generated-public-key"
VAPID_PRIVATE_KEY="your-generated-private-key"
VAPID_EMAIL="<EMAIL>"
NEXT_PUBLIC_VAPID_PUBLIC_KEY="your-generated-public-key"
```

### 2. Update Database Schema

Run the database migration to add push notification tables:

```bash
npx prisma db push
```

### 3. Add Service Worker to Your App

#### Option A: Using ServiceWorkerProvider (Recommended)

Wrap your app with the ServiceWorkerProvider:

```tsx
// app/layout.tsx or pages/_app.tsx
import { ServiceWorkerProvider } from '@/components/common/service-worker';

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        <ServiceWorkerProvider>
          {children}
        </ServiceWorkerProvider>
      </body>
    </html>
  );
}
```

#### Option B: Using ServiceWorkerInitializer

For minimal setup, just add the initializer:

```tsx
// app/layout.tsx
import { ServiceWorkerInitializer } from '@/components/common/service-worker';

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        <ServiceWorkerInitializer />
        {children}
      </body>
    </html>
  );
}
```

### 4. Add Push Notification Settings

Include the settings component in your settings page:

```tsx
// pages/settings/notifications.tsx
import { PushNotificationSettings } from '@/components/common/service-worker';

export default function NotificationSettings() {
  return (
    <div>
      <h1>Notification Settings</h1>
      <PushNotificationSettings />
    </div>
  );
}
```

## Usage

### Using the Hook

```tsx
import { usePushNotifications } from '@/components/common/service-worker';

function MyComponent() {
  const {
    isPushSupported,
    isSubscribed,
    enablePushNotifications,
    disablePushNotifications,
    showTestNotification,
  } = usePushNotifications();

  return (
    <div>
      {isPushSupported ? (
        <div>
          {isSubscribed ? (
            <button onClick={disablePushNotifications}>
              Disable Notifications
            </button>
          ) : (
            <button onClick={enablePushNotifications}>
              Enable Notifications
            </button>
          )}
          <button onClick={showTestNotification}>
            Test Notification
          </button>
        </div>
      ) : (
        <p>Push notifications not supported</p>
      )}
    </div>
  );
}
```

### Sending Push Notifications

Send push notifications from your API:

```tsx
// Send to specific user
const response = await fetch('/api/push/send', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    userId: 'user-id',
    payload: {
      title: 'New Message',
      body: 'You have a new message!',
      data: {
        type: 'chat-message',
        roomId: 'room-123',
        url: '/dashboard/chat/room-123'
      }
    }
  })
});

// Send to multiple users
const response = await fetch('/api/push/send', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    userIds: ['user-1', 'user-2', 'user-3'],
    payload: {
      title: 'System Update',
      body: 'The system has been updated with new features!'
    }
  })
});

// Send to all users
const response = await fetch('/api/push/send', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    sendToAll: true,
    payload: {
      title: 'Maintenance Notice',
      body: 'Scheduled maintenance will begin in 1 hour.'
    }
  })
});
```

## API Endpoints

### POST /api/push/subscribe
Subscribe a user to push notifications.

### DELETE /api/push/subscribe
Unsubscribe a user from push notifications.

### POST /api/push/send
Send push notifications to users.

## Service Worker Features

### Caching Strategies

- **API responses**: Network First with 5-minute cache
- **Images**: Cache First with 30-day expiration
- **CSS/JS**: Stale While Revalidate with 7-day cache
- **Fonts**: Cache First with 1-year cache
- **Documents**: Network First with 1-day cache

### Push Notification Types

The service worker handles different notification types:

- `chat-message`: Routes to `/dashboard/chat/{roomId}`
- `document-update`: Routes to `/dashboard/documents/{documentId}`
- `contract-change`: Routes to `/dashboard/contracts/{contractId}`
- `proposal-update`: Routes to `/dashboard/proposals/{proposalId}`
- `system-alert`: Routes to `/dashboard/settings/notifications`

### Background Sync

The service worker supports background sync for:
- Pending notifications when back online
- Failed API requests retry

## Troubleshooting

### Service Worker Not Registering

1. Ensure you're serving over HTTPS (required for service workers)
2. Check browser console for registration errors
3. Verify the service worker file is accessible at `/sw.js`

### Push Notifications Not Working

1. Check VAPID keys are correctly configured
2. Verify notification permission is granted
3. Ensure the user is subscribed to push notifications
4. Check browser console for push-related errors

### Database Issues

1. Run `npx prisma db push` to update the schema
2. Check database connection and permissions
3. Verify the `pushSubscription` and `notificationLog` tables exist

## Browser Support

- **Service Workers**: Chrome 40+, Firefox 44+, Safari 11.1+
- **Push Notifications**: Chrome 42+, Firefox 44+, Safari 16+
- **Background Sync**: Chrome 49+, Firefox (behind flag)

## Security Considerations

1. **VAPID Keys**: Keep private keys secure and never expose them to the client
2. **Permissions**: Always request notification permission gracefully
3. **Data**: Don't send sensitive information in push payloads
4. **Rate Limiting**: Implement rate limiting for push notification endpoints

## Performance Tips

1. **Service Worker Updates**: The service worker automatically updates and notifies users
2. **Cache Management**: Workbox handles cache expiration and cleanup
3. **Payload Size**: Keep push notification payloads small (< 4KB)
4. **Batch Notifications**: Use the batch sending API for multiple recipients
