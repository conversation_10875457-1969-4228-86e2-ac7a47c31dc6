# Socket.IO Initialization Guide

This document explains how Socket.IO was initialized and integrated into the application for real-time notifications and communication.

## Overview

The Socket.IO implementation provides real-time communication for:
- **Targeted Notifications** - Send notifications to specific users
- **Context-Based Messaging** - Room-based communication (chat, contracts)
- **User State Management** - Track online/offline status and typing indicators
- **Real-time Updates** - Instant delivery of messages and notifications

## Architecture

```
Client (React) ←→ Socket.IO Client ←→ Next.js API ←→ Socket.IO Server ←→ Database
```

## Server-Side Implementation

### 1. Socket.IO Server (`src/lib/socket/server.ts`)

**Key Features:**
- User authentication with session tokens
- Room-based communication (chat rooms, contract contexts)
- Active user connection tracking
- Targeted notification delivery

**Core Functions:**
```typescript
// Initialize server
initializeSocketServer(httpServer)

// Send to specific user
sendNotificationToUser(userId, notification)

// Send to context (room/contract)
sendNotificationToContext(contextType, contextId, notification, excludeUserId?)

// Utility functions
getActiveUsersCount()
isUserOnline(userId)
```

**Socket Events Handled:**
- `authenticate` - User authentication
- `join_context` - Join room/contract context
- `leave_context` - Leave context
- `notification_received` - Acknowledge notifications
- `disconnect` - Connection cleanup

### 2. API Route (`src/app/api/socket/route.ts`)

**Endpoints:**
- `GET /api/socket` - Check server status
- `POST /api/socket` - Initialize server (health check)

**Note:** In Next.js App Router, Socket.IO server must be initialized at the application level, not through API routes.

## Client-Side Implementation

### 1. Socket.IO Client (`src/lib/socket/client.ts`)

**Key Features:**
- Automatic connection management
- Event listener system
- Context joining/leaving
- Notification acknowledgment

**Core Functions:**
```typescript
// Connection management
initializeSocket(userId)
disconnectSocket()
isSocketConnected()

// Context management
joinContext(contextType, contextId)
leaveContext(contextType, contextId)

// Event management
addEventListener(event, listener)
removeEventListener(event, listener)
```

### 2. React Hook (`src/hooks/useSocket.ts`)

**Features:**
- Session-based authentication
- Automatic context management
- React-friendly event handling
- Notification filtering by user ID

**Usage Example:**
```typescript
const { 
  isConnected, 
  joinContext, 
  leaveContext 
} = useSocket({
  autoConnect: true,
  contexts: [
    { type: 'chat', id: 'room-123' },
    { type: 'contract', id: 'contract-456' }
  ],
  onNotification: (notification) => {
    toast.success(notification.title);
  }
});
```

## Integration with Services

### 1. Notification Service Integration

**Enhanced Methods:**
```typescript
// Create and send via Socket.IO
notificationService.createAndSendNotification(data)

// Send to specific context
notificationService.createAndSendContextNotification(
  data, 
  contextType, 
  contextId, 
  excludeUserId?
)

// Multi-user notifications
notificationService.createAndSendMultiUserNotification(
  userIds, 
  data, 
  contextType?, 
  contextId?
)
```

### 2. Chat Service Integration

**Message Notifications:**
```typescript
// In sendMessage method
await this.createMessageNotifications(
  roomId,
  messageId,
  messageContent,
  sender
);
```

**Room Creation Notifications:**
```typescript
// In createRoom method
await this.createRoomNotifications(
  roomId,
  roomName,
  creator,
  contractId?
);
```

## Environment Configuration

### Required Environment Variables

```env
# Socket.IO Configuration
NEXT_PUBLIC_SOCKET_URL=http://localhost:3000
NEXTAUTH_URL=http://localhost:3000

# For production
NEXT_PUBLIC_SOCKET_URL=https://yourdomain.com
NEXTAUTH_URL=https://yourdomain.com
```

## Usage Patterns

### 1. Chat Room Notifications

**Server-side (when message is sent):**
```typescript
// Notify all room members except sender
await notificationService.createAndSendContextNotification(
  {
    title: "New message in Project Discussion",
    message: "John: Hey everyone, check this out...",
    type: "chat",
    category: "inApp",
    userId: recipientId,
    data: {
      roomId: "room-123",
      actionUrl: "/chat/room-123"
    }
  },
  "chat",     // contextType
  "room-123", // contextId
  senderId    // excludeUserId
);
```

**Client-side (join room context):**
```typescript
// In chat room component
useEffect(() => {
  joinNotificationContext("chat", roomId);
  
  return () => {
    leaveNotificationContext("chat", roomId);
  };
}, [roomId]);
```

### 2. Contract Notifications

**Server-side (contract updates):**
```typescript
// Notify all contract participants
await notificationService.createAndSendContextNotification(
  {
    title: "Contract Updated",
    message: "Terms have been modified",
    type: "contract",
    category: "inApp",
    userId: recipientId,
    data: {
      contractId: "contract-456",
      actionUrl: "/contracts/contract-456"
    }
  },
  "contract",     // contextType
  "contract-456", // contextId
  updaterId       // excludeUserId
);
```

**Client-side (join contract context):**
```typescript
// In contract component
useEffect(() => {
  joinNotificationContext("contract", contractId);
  
  return () => {
    leaveNotificationContext("contract", contractId);
  };
}, [contractId]);
```

## Real-time Notification Flow

1. **Server Action** - User sends message/updates contract
2. **Service Layer** - ChatService/ContractService creates notification
3. **NotificationService** - Saves to database + sends via Socket.IO
4. **Socket.IO Server** - Delivers to specific context/users
5. **Client Reception** - useSocket hook receives notification
6. **UI Update** - Toast notification + data refresh
7. **User Interaction** - Click notification → navigate to source

## Best Practices

### 1. Context Management
- Always join relevant contexts when entering pages
- Clean up contexts when leaving pages
- Use specific context types (chat, contract, etc.)

### 2. Error Handling
- Notification failures should not break main functionality
- Implement fallback mechanisms for offline users
- Log all socket operations for debugging

### 3. Performance
- Use context-based rooms to limit message scope
- Implement user filtering to prevent unnecessary notifications
- Clean up connections on component unmount

### 4. Security
- Authenticate users before joining contexts
- Validate user permissions for context access
- Filter notifications by user relationships

## Troubleshooting

### Common Issues

1. **Socket not connecting**
   - Check NEXT_PUBLIC_SOCKET_URL environment variable
   - Verify server initialization
   - Check browser console for connection errors

2. **Notifications not received**
   - Ensure user is authenticated
   - Verify context joining
   - Check server logs for delivery status

3. **Multiple connections**
   - Implement proper cleanup in useEffect
   - Use connection status checks
   - Avoid multiple socket initializations

### Debug Commands

```typescript
// Check connection status
console.log(getSocketStatus());

// List active contexts
console.log(currentContexts);

// Monitor socket events
socket.onAny((event, ...args) => {
  console.log('Socket event:', event, args);
});
```

## Next Steps

1. **Server Initialization** - Set up Socket.IO server at application startup
2. **Context Integration** - Add context joining to relevant components
3. **Notification Testing** - Test real-time delivery across browser tabs
4. **Performance Monitoring** - Monitor connection health and message delivery
5. **Error Handling** - Implement comprehensive error recovery mechanisms

This Socket.IO implementation provides a robust foundation for real-time communication and notifications in the application.
